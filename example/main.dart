import 'dart:io';
import '../lib/agent/security_agent.dart';
import '../lib/agent/langgraph_workflow.dart';
import '../lib/scanner/vulnerability_database.dart';
import '../lib/scanner/frida_integration.dart';

/// Example usage of the Flutter Security Scanner Agent
Future<void> main() async {
  print('🛡️  Flutter Security Scanner - Agentic AI System');
  print('=' * 60);

  // Initialize components
  final vulnDb = VulnerabilityDatabase('.cache/vulnerabilities');
  final frida = FridaIntegration();
  
  // Initialize the AI agent
  final agent = SecurityAgent(vulnDb);
  await agent.initialize();

  // Initialize the LangGraph workflow engine
  final workflowEngine = SecurityWorkflowEngine(vulnDb, frida);

  // Example 1: Analyze a Flutter project using the AI agent
  print('\n📊 Example 1: AI Agent Analysis');
  print('-' * 40);
  
  try {
    final projectPath = './example_flutter_app';
    await _createExampleFlutterApp(projectPath);
    
    final report = await agent.analyze(projectPath);
    
    print('✅ Analysis completed!');
    print('📈 Risk Level: ${report.riskLevel}');
    print('🔍 Findings: ${report.findings.length}');
    print('🛡️  Vulnerabilities: ${report.vulnerabilities.length}');
    print('💡 Remediations: ${report.remediations.length}');
    
    // Display top findings
    if (report.findings.isNotEmpty) {
      print('\n🚨 Top Security Findings:');
      for (final finding in report.findings.take(3)) {
        print('  • ${finding.severity.toString().toUpperCase()}: ${finding.message}');
        print('    📁 ${finding.file}:${finding.line}');
      }
    }
    
  } catch (e) {
    print('❌ Error in AI agent analysis: $e');
  }

  // Example 2: Use LangGraph workflow engine
  print('\n🔄 Example 2: LangGraph Workflow Analysis');
  print('-' * 40);
  
  try {
    final projectPath = './example_flutter_app';
    final workflowReport = await workflowEngine.execute(projectPath);
    
    print('✅ Workflow completed!');
    print('📈 Risk Level: ${workflowReport.riskLevel}');
    print('🔍 Findings: ${workflowReport.findings.length}');
    print('⏱️  Execution Time: ${workflowReport.metadata['executionTime']}ms');
    
  } catch (e) {
    print('❌ Error in workflow analysis: $e');
  }

  // Example 3: Update vulnerability database
  print('\n🔄 Example 3: Vulnerability Database Update');
  print('-' * 40);
  
  try {
    await vulnDb.updateDatabase();
    
    // Query for Flutter-specific vulnerabilities
    final flutterVulns = await vulnDb.query('flutter certificate pinning', limit: 5);
    print('🔍 Found ${flutterVulns.length} Flutter-related vulnerabilities');
    
    for (final vuln in flutterVulns.take(3)) {
      print('  • ${vuln.severity.toString().toUpperCase()}: ${vuln.title}');
      print('    📝 ${vuln.description.substring(0, 80)}...');
    }
    
  } catch (e) {
    print('❌ Error updating vulnerability database: $e');
  }

  // Example 4: Frida integration check
  print('\n🔧 Example 4: Frida Integration Status');
  print('-' * 40);
  
  final fridaAvailable = await frida.isFridaAvailable();
  print('🔍 Frida Available: ${fridaAvailable ? "✅ Yes" : "❌ No"}');
  
  if (fridaAvailable) {
    final devices = await frida.getAvailableDevices();
    print('📱 Available Devices: ${devices.length}');
    for (final device in devices.take(3)) {
      print('  • $device');
    }
  } else {
    print('💡 To enable dynamic analysis, install Frida:');
    print('   pip install frida-tools');
  }

  print('\n🎉 Flutter Security Scanner demonstration completed!');
  print('📚 For more information, see the documentation in docs/');
}

/// Create an example Flutter app with security vulnerabilities for testing
Future<void> _createExampleFlutterApp(String projectPath) async {
  final dir = Directory(projectPath);
  if (!await dir.exists()) {
    await dir.create(recursive: true);
  }

  // Create lib directory
  final libDir = Directory('$projectPath/lib');
  await libDir.create(recursive: true);

  // Create main.dart with security issues
  final mainFile = File('$projectPath/lib/main.dart');
  await mainFile.writeAsString('''
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Vulnerable Flutter App',
      home: VulnerableScreen(),
    );
  }
}

class VulnerableScreen extends StatefulWidget {
  @override
  _VulnerableScreenState createState() => _VulnerableScreenState();
}

class _VulnerableScreenState extends State<VulnerableScreen> {
  static const platform = MethodChannel('com.example.vulnerable/channel');
  
  // VULNERABILITY: Hardcoded API key
  final String apiKey = "sk-1234567890abcdef1234567890abcdef";
  
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }
  
  void _initializeApp() async {
    // VULNERABILITY: Insecure storage of sensitive data
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_password', 'mySecretPassword123');
    await prefs.setString('api_token', 'bearer_token_12345');
    
    // VULNERABILITY: Insecure HTTP connection
    final client = HttpClient();
    client.badCertificateCallback = (cert, host, port) => true; // Accept all certificates
    
    // VULNERABILITY: Unvalidated method channel
    platform.setMethodCallHandler((call) async {
      if (call.method == 'executeCommand') {
        // Dangerous: executing arbitrary commands
        await Process.run('sh', ['-c', call.arguments]);
      }
      return null;
    });
  }
  
  void _makeInsecureRequest() async {
    // VULNERABILITY: HTTP instead of HTTPS
    final url = 'http://api.example.com/user/data?key=\$apiKey';
    final client = HttpClient();
    
    try {
      final request = await client.getUrl(Uri.parse(url));
      final response = await request.close();
      print('Response: \${response.statusCode}');
    } catch (e) {
      print('Error: \$e');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Vulnerable App')),
      body: Center(
        child: Column(
          children: [
            ElevatedButton(
              onPressed: _makeInsecureRequest,
              child: Text('Make Insecure Request'),
            ),
            Text('API Key: \$apiKey'), // VULNERABILITY: Exposing secrets in UI
          ],
        ),
      ),
    );
  }
}
''');

  // Create pubspec.yaml
  final pubspecFile = File('$projectPath/pubspec.yaml');
  await pubspecFile.writeAsString('''
name: vulnerable_flutter_app
description: Example Flutter app with security vulnerabilities for testing

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  shared_preferences: ^2.0.15
  http: ^0.13.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true
''');

  // Create network service with more vulnerabilities
  final networkFile = File('$projectPath/lib/network_service.dart');
  await networkFile.writeAsString('''
import 'dart:io';
import 'dart:convert';

class NetworkService {
  // VULNERABILITY: Hardcoded credentials
  static const String username = "admin";
  static const String password = "password123";
  
  // VULNERABILITY: Weak encryption
  static const String encryptionKey = "DES"; // Weak algorithm
  
  Future<Map<String, dynamic>> login() async {
    final client = HttpClient();
    
    // VULNERABILITY: Disable certificate validation
    client.badCertificateCallback = (cert, host, port) {
      print('Accepting bad certificate for \$host:\$port');
      return true; // Always accept
    };
    
    try {
      // VULNERABILITY: HTTP instead of HTTPS for sensitive data
      final request = await client.postUrl(
        Uri.parse('http://api.vulnerable-app.com/login')
      );
      
      request.headers.set('Content-Type', 'application/json');
      
      // VULNERABILITY: Sending credentials in plain text
      final body = jsonEncode({
        'username': username,
        'password': password,
        'api_key': 'sk-1234567890abcdef', // Hardcoded API key
      });
      
      request.write(body);
      final response = await request.close();
      
      if (response.statusCode == 200) {
        final responseBody = await response.transform(utf8.decoder).join();
        return jsonDecode(responseBody);
      }
    } catch (e) {
      print('Login error: \$e');
    }
    
    return {};
  }
  
  // VULNERABILITY: SQL injection potential
  Future<List<dynamic>> getUserData(String userId) async {
    // This would be vulnerable to SQL injection if connected to a database
    final query = "SELECT * FROM users WHERE id = '\$userId'";
    print('Executing query: \$query');
    
    // Simulated database response
    return [
      {'id': userId, 'name': 'User', 'email': '<EMAIL>'}
    ];
  }
}
''');

  print('📁 Created example Flutter app with security vulnerabilities at: $projectPath');
}
