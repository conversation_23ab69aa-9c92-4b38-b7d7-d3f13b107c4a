enum Severity { low, medium, high, critical }

class Finding {
  final String file;
  final int line;
  final String ruleId;
  final String message;
  final Severity severity;
  final bool confirmed;

  Finding({
    required this.file,
    required this.line,
    required this.ruleId,
    required this.message,
    required this.severity,
    this.confirmed = false,
  });
}

class Rule {
  final String id;
  final String description;
  final Severity defaultSeverity;
  Rule(this.id, this.description, this.defaultSeverity);
}