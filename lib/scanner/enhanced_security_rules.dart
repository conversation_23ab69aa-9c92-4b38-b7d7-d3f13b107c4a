import 'package:analyzer/dart/ast/ast.dart';
import 'package:analyzer/dart/ast/visitor.dart';
import 'package:analyzer/source/line_info.dart';
import '../models.dart';

/// Enhanced security rules covering comprehensive Flutter vulnerabilities
class EnhancedSecurityRules {
  static List<Finding> runAll(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    // Certificate and TLS Security
    findings.addAll(_checkCertificatePinning(root, file, lineInfo));
    findings.addAll(_checkTlsConfiguration(root, file, lineInfo));
    findings.addAll(_checkInsecureNetworking(root, file, lineInfo));

    // Data Security
    findings.addAll(_checkInsecureStorage(root, file, lineInfo));
    findings.addAll(_checkSensitiveDataLogging(root, file, lineInfo));
    findings.addAll(_checkHardcodedSecrets(root, file, lineInfo));

    // Platform Integration Security
    findings.addAll(_checkWebViewSecurity(root, file, lineInfo));
    findings.addAll(_checkDeepLinkSecurity(root, file, lineInfo));
    findings.addAll(_checkMethodChannelSecurity(root, file, lineInfo));

    // Cryptographic Security
    findings.addAll(_checkWeakCryptography(root, file, lineInfo));
    findings.addAll(_checkInsecureRandomness(root, file, lineInfo));
    findings.addAll(_checkKeyManagement(root, file, lineInfo));

    // Runtime Security
    findings.addAll(_checkAntiTamperingMechanisms(root, file, lineInfo));
    findings.addAll(_checkDebugInformation(root, file, lineInfo));
    findings.addAll(_checkCodeObfuscation(root, file, lineInfo));

    return findings;
  }

  /// Check for missing or weak certificate pinning
  static List<Finding> _checkCertificatePinning(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    bool hasCertificatePinning = false;
    bool hasInsecureCallback = false;

    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation node) {
        final source = node.toSource();

        // Check for certificate pinning implementations
        if (source.contains('CertificatePinner') ||
            source.contains('certificate_pinning') ||
            source.contains('ssl_pinning')) {
          hasCertificatePinning = true;
        }

        // Check for insecure certificate callbacks
        if (source.contains('badCertificateCallback') ||
            source.contains('allowBadCertificates')) {
          hasInsecureCallback = true;
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "TLS001",
            message:
                "Insecure certificate validation - allows bad certificates",
            severity: Severity.critical,
          ));
        }
      });

    // If no certificate pinning found in network-related files
    if (!hasCertificatePinning &&
        (file.contains('network') ||
            file.contains('api') ||
            file.contains('http'))) {
      findings.add(Finding(
        file: file,
        line: 1,
        ruleId: "TLS002",
        message: "Missing certificate pinning implementation",
        severity: Severity.high,
      ));
    }

    return findings;
  }

  /// Check TLS configuration issues
  static List<Finding> _checkTlsConfiguration(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitStringLiteral = (StringLiteral node) {
        final value = node.stringValue?.toLowerCase() ?? '';

        // Check for weak TLS versions
        if (value.contains('tlsv1.0') ||
            value.contains('tlsv1.1') ||
            value.contains('sslv3')) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "TLS003",
            message: "Weak TLS version detected: $value",
            severity: Severity.high,
          ));
        }

        // Check for weak cipher suites
        if (value.contains('rc4') ||
            value.contains('des') ||
            value.contains('md5')) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "TLS004",
            message: "Weak cipher suite detected: $value",
            severity: Severity.high,
          ));
        }
      });

    return findings;
  }

  /// Check for insecure networking practices
  static List<Finding> _checkInsecureNetworking(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitStringLiteral = (StringLiteral node) {
        final value = node.stringValue ?? '';

        // Check for HTTP URLs in production code
        if (value.startsWith('http://') &&
            !value.contains('localhost') &&
            !value.contains('127.0.0.1')) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "NET001",
            message: "Insecure HTTP URL detected: $value",
            severity: Severity.medium,
          ));
        }
      });

    return findings;
  }

  /// Check for insecure data storage
  static List<Finding> _checkInsecureStorage(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation node) {
        final source = node.toSource();

        // Check for sensitive data in SharedPreferences
        if (source.contains('SharedPreferences') ||
            source.contains('setString')) {
          for (var arg in node.argumentList.arguments) {
            final argSource = arg.toSource().toLowerCase();
            if (argSource.contains('password') ||
                argSource.contains('token') ||
                argSource.contains('secret') ||
                argSource.contains('key')) {
              final loc = lineInfo.getLocation(node.offset);
              findings.add(Finding(
                file: file,
                line: loc.lineNumber,
                ruleId: "STO001",
                message:
                    "Sensitive data stored in SharedPreferences without encryption",
                severity: Severity.critical,
              ));
            }
          }
        }

        // Check for file storage without encryption
        if (source.contains('writeAsString') ||
            source.contains('writeAsBytes')) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "STO002",
            message: "Data written to file without encryption verification",
            severity: Severity.medium,
          ));
        }
      });

    return findings;
  }

  /// Check for sensitive data in logs
  static List<Finding> _checkSensitiveDataLogging(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation node) {
        if (node.methodName.name == 'print' ||
            node.toSource().contains('log') ||
            node.toSource().contains('debug')) {
          for (var arg in node.argumentList.arguments) {
            final argSource = arg.toSource().toLowerCase();
            if (argSource.contains('password') ||
                argSource.contains('token') ||
                argSource.contains('secret') ||
                argSource.contains('credit') ||
                argSource.contains('ssn') ||
                argSource.contains('email')) {
              final loc = lineInfo.getLocation(node.offset);
              findings.add(Finding(
                file: file,
                line: loc.lineNumber,
                ruleId: "LOG001",
                message:
                    "Sensitive information logged: potential data exposure",
                severity: Severity.high,
              ));
            }
          }
        }
      });

    return findings;
  }

  /// Check for hardcoded secrets and credentials
  static List<Finding> _checkHardcodedSecrets(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitStringLiteral = (StringLiteral node) {
        final value = node.stringValue ?? '';

        // Check for API keys, tokens, passwords
        if (_isLikelySecret(value)) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "SEC001",
            message: "Potential hardcoded secret detected",
            severity: Severity.critical,
          ));
        }
      });

    return findings;
  }

  /// Check WebView security configurations
  static List<Finding> _checkWebViewSecurity(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitInstanceCreationExpression = (InstanceCreationExpression expr) {
        final source = expr.toSource();

        if (source.contains('WebView')) {
          // Check for unrestricted JavaScript
          if (source.contains('JavascriptMode.unrestricted')) {
            final loc = lineInfo.getLocation(expr.offset);
            findings.add(Finding(
              file: file,
              line: loc.lineNumber,
              ruleId: "WEB001",
              message: "WebView allows unrestricted JavaScript execution",
              severity: Severity.high,
            ));
          }

          // Check for file access
          if (source.contains('allowsInlineMediaPlayback') ||
              source.contains('allowFileAccess')) {
            final loc = lineInfo.getLocation(expr.offset);
            findings.add(Finding(
              file: file,
              line: loc.lineNumber,
              ruleId: "WEB002",
              message: "WebView allows potentially unsafe file access",
              severity: Severity.medium,
            ));
          }
        }
      });

    return findings;
  }

  /// Check deep link security
  static List<Finding> _checkDeepLinkSecurity(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation node) {
        final source = node.toSource();

        // Check for unvalidated deep link handling
        if (source.contains('Uri.parse') || source.contains('linkStream')) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "LINK001",
            message: "Deep link handling requires input validation",
            severity: Severity.medium,
          ));
        }
      });

    return findings;
  }

  /// Check method channel security
  static List<Finding> _checkMethodChannelSecurity(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitInstanceCreationExpression = (InstanceCreationExpression expr) {
        if (expr.constructorName.toSource().contains('MethodChannel')) {
          final loc = lineInfo.getLocation(expr.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "CHAN001",
            message:
                "MethodChannel requires input validation and access control",
            severity: Severity.high,
          ));
        }
      });

    return findings;
  }

  /// Helper method to detect likely secrets
  static bool _isLikelySecret(String value) {
    if (value.length < 8) return false;

    // Check for high entropy (likely random strings)
    final entropy = _calculateEntropy(value);
    if (entropy > 4.5 && value.length > 16) return true;

    // Check for common secret patterns
    final patterns = [
      RegExp(r'^[A-Za-z0-9+/]{40,}={0,2}$'), // Base64
      RegExp(r'^[a-f0-9]{32,}$'), // Hex
      RegExp(r'^[A-Z0-9]{20,}$'), // API keys
    ];

    return patterns.any((pattern) => pattern.hasMatch(value));
  }

  /// Calculate Shannon entropy of a string
  static double _calculateEntropy(String data) {
    if (data.isEmpty) return 0.0;

    final frequencies = <int, int>{};
    for (int i = 0; i < data.length; i++) {
      final char = data.codeUnitAt(i);
      frequencies[char] = (frequencies[char] ?? 0) + 1;
    }

    double entropy = 0.0;
    final length = data.length;

    for (final count in frequencies.values) {
      final probability = count / length;
      entropy -= probability *
          (probability > 0 ? (probability * 3.321928) : 0); // log2
    }

    return entropy;
  }

  /// Check for weak cryptographic implementations
  static List<Finding> _checkWeakCryptography(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitStringLiteral = (StringLiteral node) {
        final value = node.stringValue?.toUpperCase() ?? '';

        // Check for weak encryption algorithms
        if (value.contains('DES') ||
            value.contains('RC4') ||
            value.contains('MD5') ||
            value.contains('SHA1')) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "CRY001",
            message: "Weak cryptographic algorithm detected: $value",
            severity: Severity.critical,
          ));
        }

        // Check for ECB mode
        if (value.contains('AES/ECB') || value.contains('ECB')) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "CRY002",
            message: "Insecure ECB encryption mode detected",
            severity: Severity.high,
          ));
        }
      });

    return findings;
  }

  /// Check for insecure random number generation
  static List<Finding> _checkInsecureRandomness(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitInstanceCreationExpression = (InstanceCreationExpression expr) {
        if (expr.constructorName.toSource().contains('Random(')) {
          final loc = lineInfo.getLocation(expr.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "RND001",
            message: "Use SecureRandom for cryptographic operations",
            severity: Severity.medium,
          ));
        }
      });

    return findings;
  }

  /// Check key management practices
  static List<Finding> _checkKeyManagement(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitStringLiteral = (StringLiteral node) {
        final value = node.stringValue ?? '';

        // Check for hardcoded encryption keys
        if (value.length >= 16 && _isLikelyEncryptionKey(value)) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "KEY001",
            message: "Potential hardcoded encryption key detected",
            severity: Severity.critical,
          ));
        }
      });

    return findings;
  }

  /// Check for anti-tampering mechanisms
  static List<Finding> _checkAntiTamperingMechanisms(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    bool hasRootDetection = false;
    bool hasDebugDetection = false;
    bool hasEmulatorDetection = false;

    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation node) {
        final source = node.toSource().toLowerCase();

        if (source.contains('isrooted') || source.contains('rootdetection')) {
          hasRootDetection = true;
        }
        if (source.contains('isdebug') || source.contains('debugmode')) {
          hasDebugDetection = true;
        }
        if (source.contains('emulator') || source.contains('simulator')) {
          hasEmulatorDetection = true;
        }
      });

    // Report missing anti-tampering mechanisms
    if (!hasRootDetection && file.contains('main.dart')) {
      findings.add(Finding(
        file: file,
        line: 1,
        ruleId: "ANTI001",
        message: "Missing root/jailbreak detection mechanism",
        severity: Severity.medium,
      ));
    }

    if (!hasDebugDetection && file.contains('main.dart')) {
      findings.add(Finding(
        file: file,
        line: 1,
        ruleId: "ANTI002",
        message: "Missing debug detection mechanism",
        severity: Severity.low,
      ));
    }

    return findings;
  }

  /// Check for debug information exposure
  static List<Finding> _checkDebugInformation(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitBooleanLiteral = (BooleanLiteral node) {
        // Check for debug flags
        if (node.value == true) {
          final parent = node.parent;
          if (parent is AssignmentExpression) {
            final target = parent.leftHandSide.toSource().toLowerCase();
            if (target.contains('debug') || target.contains('verbose')) {
              final loc = lineInfo.getLocation(node.offset);
              findings.add(Finding(
                file: file,
                line: loc.lineNumber,
                ruleId: "DBG001",
                message: "Debug flag enabled in production code",
                severity: Severity.low,
              ));
            }
          }
        }
      });

    return findings;
  }

  /// Check for code obfuscation
  static List<Finding> _checkCodeObfuscation(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    // This is a basic check - in practice, you'd analyze build configurations
    // For now, we'll check if there are any obfuscation-related imports or configurations

    root.visitChildren(SimpleAstVisitor()
      ..visitImportDirective = (ImportDirective node) {
        final uri = node.uri.stringValue ?? '';
        if (uri.contains('obfuscation') || uri.contains('proguard')) {
          // Good - obfuscation tools detected
          return;
        }
      });

    // If this is a main file and no obfuscation detected, report it
    if (file.contains('main.dart')) {
      findings.add(Finding(
        file: file,
        line: 1,
        ruleId: "OBF001",
        message: "Consider implementing code obfuscation for production builds",
        severity: Severity.low,
      ));
    }

    return findings;
  }

  /// Helper to detect likely encryption keys
  static bool _isLikelyEncryptionKey(String value) {
    // Check for common key lengths (128, 192, 256 bits in hex or base64)
    final hexPattern =
        RegExp(r'^[a-fA-F0-9]{32}$|^[a-fA-F0-9]{48}$|^[a-fA-F0-9]{64}$');
    final base64Pattern = RegExp(
        r'^[A-Za-z0-9+/]{22}={2}$|^[A-Za-z0-9+/]{32}$|^[A-Za-z0-9+/]{44}={0,2}$');

    return hexPattern.hasMatch(value) || base64Pattern.hasMatch(value);
  }
}
