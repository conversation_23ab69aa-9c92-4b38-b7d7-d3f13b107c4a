import 'package:analyzer/dart/ast/ast.dart';
import 'package:analyzer/dart/ast/visitor.dart';
import 'package:analyzer/source/line_info.dart';
import '../models.dart';

class TaintEngine {
  final Set<String> sources = {"http.get", "dio.get", "stdin.readLineSync"};
  final Set<String> sinks = {"MethodChannel.invokeMethod", "eval", "File.writeAsString"};

  List<Finding> analyze(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];

    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation call) {
        final callName = call.methodName.name;
        if (sinks.contains(callName)) {
          for (var arg in call.argumentList.arguments) {
            if (arg.toSource().contains("input")) {
              final loc = lineInfo.getLocation(call.offset);
              findings.add(Finding(
                file: file,
                line: loc.lineNumber,
                ruleId: "TAINT001",
                message: "Tainted input flows into $callName.",
                severity: Severity.critical,
              ));
            }
          }
        }
      });

    return findings;
  }
}
