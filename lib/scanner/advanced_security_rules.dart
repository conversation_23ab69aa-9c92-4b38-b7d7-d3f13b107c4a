import 'package:analyzer/dart/ast/ast.dart';
import 'package:analyzer/dart/ast/visitor.dart';
import 'package:analyzer/source/line_info.dart';
import '../models.dart';
import 'dart:io';

/// Advanced security rules for comprehensive Flutter security analysis
class AdvancedSecurityRules {
  
  /// Run all advanced security checks
  static List<Finding> runAll(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    findings.addAll(_checkAntiTamperingMechanisms(root, file, lineInfo));
    findings.addAll(_checkCodeObfuscation(root, file, lineInfo));
    findings.addAll(_checkNativeCodeSecurity(root, file, lineInfo));
    findings.addAll(_checkPlatformSpecificVulnerabilities(root, file, lineInfo));
    findings.addAll(_checkRuntimeApplicationSelfProtection(root, file, lineInfo));
    findings.addAll(_checkBinaryPackingDetection(root, file, lineInfo));
    findings.addAll(_checkDebuggingProtection(root, file, lineInfo));
    return findings;
  }

  /// Check for anti-tampering mechanisms
  static List<Finding> _checkAntiTamperingMechanisms(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _AntiTamperingVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  /// Check for code obfuscation analysis
  static List<Finding> _checkCodeObfuscation(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _CodeObfuscationVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  /// Check for native code security issues
  static List<Finding> _checkNativeCodeSecurity(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _NativeCodeSecurityVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  /// Check for platform-specific vulnerabilities
  static List<Finding> _checkPlatformSpecificVulnerabilities(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _PlatformSpecificVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  /// Check for Runtime Application Self Protection (RASP)
  static List<Finding> _checkRuntimeApplicationSelfProtection(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _RASPVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  /// Check for binary packing detection
  static List<Finding> _checkBinaryPackingDetection(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _BinaryPackingVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  /// Check for debugging protection
  static List<Finding> _checkDebuggingProtection(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _DebuggingProtectionVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }
}

/// Anti-tampering mechanisms visitor
class _AntiTamperingVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _AntiTamperingVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitMethodInvocation(MethodInvocation node) {
    final source = node.toSource();
    
    // Check for missing integrity checks
    if (source.contains('PackageInfo') && !source.contains('signature')) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "ANTI001",
        message: "Missing app signature verification - vulnerable to repackaging",
        severity: Severity.high,
      ));
    }

    // Check for missing root/jailbreak detection
    if (source.contains('main(') && !_hasRootDetection()) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "ANTI002",
        message: "Missing root/jailbreak detection mechanism",
        severity: Severity.medium,
      ));
    }

    // Check for missing emulator detection
    if (source.contains('main(') && !_hasEmulatorDetection()) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "ANTI003",
        message: "Missing emulator detection mechanism",
        severity: Severity.medium,
      ));
    }

    super.visitMethodInvocation(node);
  }

  bool _hasRootDetection() {
    // This would check if root detection is implemented
    // For now, return false to highlight the missing protection
    return false;
  }

  bool _hasEmulatorDetection() {
    // This would check if emulator detection is implemented
    return false;
  }
}

/// Code obfuscation analysis visitor
class _CodeObfuscationVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _CodeObfuscationVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitSimpleIdentifier(SimpleIdentifier node) {
    final name = node.name;
    
    // Check for non-obfuscated sensitive method names
    if (_isSensitiveMethodName(name) && !_isObfuscated(name)) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "OBFS001",
        message: "Sensitive method '$name' not obfuscated - vulnerable to reverse engineering",
        severity: Severity.medium,
      ));
    }

    super.visitSimpleIdentifier(node);
  }

  @override
  void visitSimpleStringLiteral(SimpleStringLiteral node) {
    final value = node.value;
    
    // Check for hardcoded sensitive strings
    if (_isSensitiveString(value)) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "OBFS002",
        message: "Sensitive string '$value' not obfuscated - visible in binary",
        severity: Severity.high,
      ));
    }

    super.visitSimpleStringLiteral(node);
  }

  bool _isSensitiveMethodName(String name) {
    final sensitiveNames = [
      'authenticate', 'login', 'decrypt', 'encrypt', 'validateSignature',
      'checkLicense', 'verifyPurchase', 'unlockFeature'
    ];
    return sensitiveNames.any((sensitive) => name.toLowerCase().contains(sensitive.toLowerCase()));
  }

  bool _isSensitiveString(String value) {
    final sensitivePatterns = [
      RegExp(r'api[_-]?key', caseSensitive: false),
      RegExp(r'secret[_-]?key', caseSensitive: false),
      RegExp(r'private[_-]?key', caseSensitive: false),
      RegExp(r'license[_-]?key', caseSensitive: false),
      RegExp(r'encryption[_-]?key', caseSensitive: false),
    ];
    return sensitivePatterns.any((pattern) => pattern.hasMatch(value));
  }

  bool _isObfuscated(String name) {
    // Simple heuristic: obfuscated names are typically short and non-descriptive
    return name.length <= 3 || !RegExp(r'^[a-zA-Z][a-zA-Z0-9]*$').hasMatch(name);
  }
}

/// Native code security visitor
class _NativeCodeSecurityVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _NativeCodeSecurityVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitMethodInvocation(MethodInvocation node) {
    final source = node.toSource();
    
    // Check for unsafe native method calls
    if (source.contains('MethodChannel') && source.contains('invokeMethod')) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "NATIVE001",
        message: "Native method call without input validation - potential buffer overflow",
        severity: Severity.high,
      ));
    }

    // Check for FFI usage without bounds checking
    if (source.contains('ffi.') && !source.contains('bounds')) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "NATIVE002",
        message: "FFI call without bounds checking - potential memory corruption",
        severity: Severity.critical,
      ));
    }

    // Check for unsafe pointer operations
    if (source.contains('Pointer') && source.contains('cast')) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "NATIVE003",
        message: "Unsafe pointer casting - potential memory safety issue",
        severity: Severity.high,
      ));
    }

    super.visitMethodInvocation(node);
  }
}

/// Platform-specific vulnerabilities visitor
class _PlatformSpecificVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _PlatformSpecificVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitMethodInvocation(MethodInvocation node) {
    final source = node.toSource();
    
    // Android-specific vulnerabilities
    if (source.contains('Platform.isAndroid')) {
      _checkAndroidVulnerabilities(node, source);
    }

    // iOS-specific vulnerabilities
    if (source.contains('Platform.isIOS')) {
      _checkIOSVulnerabilities(node, source);
    }

    super.visitMethodInvocation(node);
  }

  void _checkAndroidVulnerabilities(MethodInvocation node, String source) {
    // Check for insecure Android-specific practices
    if (source.contains('Intent') && !source.contains('setComponent')) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "ANDROID001",
        message: "Implicit Intent usage - vulnerable to Intent hijacking",
        severity: Severity.medium,
      ));
    }

    // Check for missing Android backup restrictions
    if (source.contains('SharedPreferences') && !source.contains('allowBackup')) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "ANDROID002",
        message: "SharedPreferences without backup restrictions - data leakage risk",
        severity: Severity.medium,
      ));
    }
  }

  void _checkIOSVulnerabilities(MethodInvocation node, String source) {
    // Check for iOS-specific vulnerabilities
    if (source.contains('Keychain') && !source.contains('kSecAttrAccessible')) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "IOS001",
        message: "Keychain access without proper accessibility constraints",
        severity: Severity.medium,
      ));
    }

    // Check for missing iOS App Transport Security
    if (source.contains('http://') && !source.contains('NSExceptionDomains')) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "IOS002",
        message: "HTTP connection without ATS exception - potential MITM attack",
        severity: Severity.high,
      ));
    }
  }
}

/// Runtime Application Self Protection (RASP) visitor
class _RASPVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _RASPVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitMethodInvocation(MethodInvocation node) {
    final source = node.toSource();
    
    // Check for missing runtime protection
    if (source.contains('main(') && !_hasRuntimeProtection()) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "RASP001",
        message: "Missing runtime application self-protection mechanisms",
        severity: Severity.medium,
      ));
    }

    super.visitMethodInvocation(node);
  }

  bool _hasRuntimeProtection() {
    // Check if runtime protection is implemented
    return false;
  }
}

/// Binary packing detection visitor
class _BinaryPackingVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _BinaryPackingVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitMethodInvocation(MethodInvocation node) {
    // This would analyze the binary for packing/compression
    // For now, we'll check for indicators in the source
    final source = node.toSource();
    
    if (source.contains('main(') && !_hasBinaryProtection()) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "PACK001",
        message: "Binary not protected with packing/obfuscation - easy to reverse engineer",
        severity: Severity.low,
      ));
    }

    super.visitMethodInvocation(node);
  }

  bool _hasBinaryProtection() {
    // Check if binary protection is applied
    return false;
  }
}

/// Debugging protection visitor
class _DebuggingProtectionVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _DebuggingProtectionVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitMethodInvocation(MethodInvocation node) {
    final source = node.toSource();
    
    // Check for debug mode detection
    if (source.contains('kDebugMode') && source.contains('true')) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "DEBUG001",
        message: "Debug mode enabled in production - security risk",
        severity: Severity.medium,
      ));
    }

    // Check for missing anti-debugging protection
    if (source.contains('main(') && !_hasAntiDebuggingProtection()) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "DEBUG002",
        message: "Missing anti-debugging protection mechanisms",
        severity: Severity.medium,
      ));
    }

    super.visitMethodInvocation(node);
  }

  bool _hasAntiDebuggingProtection() {
    // Check if anti-debugging protection is implemented
    return false;
  }
}
