import 'dart:io';
import 'package:yaml/yaml.dart';

class Config {
  final Map<String, bool> rules;
  final double entropyThreshold;
  final List<Map<String, String>> suppressions;

  Config(this.rules, this.entropyThreshold, this.suppressions);

  factory Config.load([String path = ".fluttersec.yaml"]) {
    if (!File(path).existsSync()) {
      return Config({}, 0.8, []);
    }
    final yaml = loadYaml(File(path).readAsStringSync());
    return Config(
      Map<String, bool>.from(yaml["rules"] ?? {}),
      (yaml["entropy_threshold"] ?? 0.8).toDouble(),
      List<Map<String, String>>.from(yaml["suppressions"] ?? []),
    );
  }

  bool isSuppressed(String file, String ruleId) {
    return suppressions.any((s) => s["file"] == file && s["rule"] == ruleId);
  }
}
