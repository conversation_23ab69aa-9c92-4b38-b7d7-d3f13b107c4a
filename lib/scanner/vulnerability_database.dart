import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import '../models.dart';

/// Vulnerability database entry
class VulnerabilityEntry {
  final String id;
  final String title;
  final String description;
  final Severity severity;
  final List<String> tags;
  final String source;
  final DateTime lastUpdated;
  final Map<String, dynamic> metadata;

  VulnerabilityEntry({
    required this.id,
    required this.title,
    required this.description,
    required this.severity,
    required this.tags,
    required this.source,
    required this.lastUpdated,
    this.metadata = const {},
  });

  factory VulnerabilityEntry.fromJson(Map<String, dynamic> json) {
    return VulnerabilityEntry(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      severity: _parseSeverity(json['severity']),
      tags: List<String>.from(json['tags'] ?? []),
      source: json['source'] ?? '',
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'severity': severity.toString(),
      'tags': tags,
      'source': source,
      'lastUpdated': lastUpdated.toIso8601String(),
      'metadata': metadata,
    };
  }

  static Severity _parseSeverity(dynamic severity) {
    if (severity is String) {
      switch (severity.toLowerCase()) {
        case 'critical': return Severity.critical;
        case 'high': return Severity.high;
        case 'medium': return Severity.medium;
        case 'low': return Severity.low;
        default: return Severity.medium;
      }
    }
    return Severity.medium;
  }
}

/// RAG-based vulnerability database system
class VulnerabilityDatabase {
  final String _cacheDir;
  final Map<String, VulnerabilityEntry> _cache = {};
  final Duration _cacheExpiry = const Duration(hours: 24);

  VulnerabilityDatabase(this._cacheDir);

  /// Initialize the database and load cached data
  Future<void> initialize() async {
    await _ensureCacheDir();
    await _loadCache();
  }

  /// Query vulnerabilities by keywords and context
  Future<List<VulnerabilityEntry>> query(String query, {
    List<String> tags = const [],
    Severity? minSeverity,
    int limit = 10,
  }) async {
    // First, search in local cache
    final localResults = _searchLocal(query, tags: tags, minSeverity: minSeverity);
    
    // If we have enough results, return them
    if (localResults.length >= limit) {
      return localResults.take(limit).toList();
    }

    // Otherwise, fetch from external sources
    final externalResults = await _fetchFromSources(query, tags: tags, minSeverity: minSeverity);
    
    // Combine and deduplicate results
    final allResults = <String, VulnerabilityEntry>{};
    for (final entry in localResults) {
      allResults[entry.id] = entry;
    }
    for (final entry in externalResults) {
      allResults[entry.id] = entry;
    }

    // Sort by relevance and severity
    final sortedResults = allResults.values.toList()
      ..sort((a, b) {
        // First by severity (critical > high > medium > low)
        final severityOrder = [Severity.critical, Severity.high, Severity.medium, Severity.low];
        final aSeverityIndex = severityOrder.indexOf(a.severity);
        final bSeverityIndex = severityOrder.indexOf(b.severity);
        
        if (aSeverityIndex != bSeverityIndex) {
          return aSeverityIndex.compareTo(bSeverityIndex);
        }
        
        // Then by relevance (simple keyword matching for now)
        final aRelevance = _calculateRelevance(a, query);
        final bRelevance = _calculateRelevance(b, query);
        return bRelevance.compareTo(aRelevance);
      });

    return sortedResults.take(limit).toList();
  }

  /// Update the vulnerability database from external sources
  Future<void> updateDatabase() async {
    print('Updating vulnerability database...');
    
    // Fetch from multiple sources in parallel
    final futures = [
      _fetchFromOSV(),
      _fetchFromOWASP(),
      _fetchFromFlutterSecurity(),
    ];

    final results = await Future.wait(futures);
    
    // Merge all results
    for (final entries in results) {
      for (final entry in entries) {
        _cache[entry.id] = entry;
      }
    }

    // Save updated cache
    await _saveCache();
    print('Database updated with ${_cache.length} entries');
  }

  /// Search local cache
  List<VulnerabilityEntry> _searchLocal(String query, {
    List<String> tags = const [],
    Severity? minSeverity,
  }) {
    final queryLower = query.toLowerCase();
    final results = <VulnerabilityEntry>[];

    for (final entry in _cache.values) {
      // Check severity filter
      if (minSeverity != null) {
        final severityOrder = [Severity.low, Severity.medium, Severity.high, Severity.critical];
        if (severityOrder.indexOf(entry.severity) < severityOrder.indexOf(minSeverity)) {
          continue;
        }
      }

      // Check tag filter
      if (tags.isNotEmpty && !tags.any((tag) => entry.tags.contains(tag))) {
        continue;
      }

      // Check query match
      if (_matchesQuery(entry, queryLower)) {
        results.add(entry);
      }
    }

    return results;
  }

  /// Check if entry matches query
  bool _matchesQuery(VulnerabilityEntry entry, String queryLower) {
    return entry.title.toLowerCase().contains(queryLower) ||
           entry.description.toLowerCase().contains(queryLower) ||
           entry.tags.any((tag) => tag.toLowerCase().contains(queryLower));
  }

  /// Calculate relevance score for ranking
  double _calculateRelevance(VulnerabilityEntry entry, String query) {
    final queryLower = query.toLowerCase();
    double score = 0.0;

    // Title match (highest weight)
    if (entry.title.toLowerCase().contains(queryLower)) {
      score += 10.0;
    }

    // Description match
    if (entry.description.toLowerCase().contains(queryLower)) {
      score += 5.0;
    }

    // Tag match
    for (final tag in entry.tags) {
      if (tag.toLowerCase().contains(queryLower)) {
        score += 3.0;
      }
    }

    // Recency bonus
    final daysSinceUpdate = DateTime.now().difference(entry.lastUpdated).inDays;
    if (daysSinceUpdate < 30) {
      score += 2.0;
    } else if (daysSinceUpdate < 90) {
      score += 1.0;
    }

    return score;
  }

  /// Fetch vulnerabilities from external sources
  Future<List<VulnerabilityEntry>> _fetchFromSources(String query, {
    List<String> tags = const [],
    Severity? minSeverity,
  }) async {
    // This would implement actual API calls to external sources
    // For now, return empty list as placeholder
    return [];
  }

  /// Fetch from OSV.dev API
  Future<List<VulnerabilityEntry>> _fetchFromOSV() async {
    try {
      final response = await http.post(
        Uri.parse('https://api.osv.dev/v1/query'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'query': 'flutter',
          'page_token': '',
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final vulnerabilities = <VulnerabilityEntry>[];

        for (final vuln in data['vulns'] ?? []) {
          vulnerabilities.add(VulnerabilityEntry(
            id: vuln['id'] ?? '',
            title: vuln['summary'] ?? '',
            description: vuln['details'] ?? '',
            severity: _mapOSVSeverity(vuln),
            tags: ['flutter', 'osv'],
            source: 'OSV.dev',
            lastUpdated: DateTime.parse(vuln['modified'] ?? DateTime.now().toIso8601String()),
            metadata: vuln,
          ));
        }

        return vulnerabilities;
      }
    } catch (e) {
      print('Error fetching from OSV.dev: $e');
    }

    return [];
  }

  /// Fetch from OWASP Mobile Top 10
  Future<List<VulnerabilityEntry>> _fetchFromOWASP() async {
    // Placeholder for OWASP Mobile Top 10 data
    // In a real implementation, this would fetch from OWASP APIs or repositories
    return [
      VulnerabilityEntry(
        id: 'OWASP-M1',
        title: 'Improper Platform Usage',
        description: 'Misuse of a platform feature or failure to use platform security controls',
        severity: Severity.high,
        tags: ['owasp', 'mobile', 'platform'],
        source: 'OWASP Mobile Top 10',
        lastUpdated: DateTime.now(),
      ),
      VulnerabilityEntry(
        id: 'OWASP-M2',
        title: 'Insecure Data Storage',
        description: 'Insecure data storage vulnerabilities occur when development teams assume that users or malware will not have access to a mobile device\'s filesystem',
        severity: Severity.high,
        tags: ['owasp', 'mobile', 'data-storage'],
        source: 'OWASP Mobile Top 10',
        lastUpdated: DateTime.now(),
      ),
      // Add more OWASP entries...
    ];
  }

  /// Fetch Flutter-specific security issues
  Future<List<VulnerabilityEntry>> _fetchFromFlutterSecurity() async {
    // Placeholder for Flutter-specific security database
    return [
      VulnerabilityEntry(
        id: 'FLUTTER-TLS-001',
        title: 'Certificate Pinning Bypass',
        description: 'Flutter apps without proper certificate pinning can be vulnerable to man-in-the-middle attacks',
        severity: Severity.critical,
        tags: ['flutter', 'tls', 'certificate-pinning'],
        source: 'Flutter Security Database',
        lastUpdated: DateTime.now(),
      ),
      VulnerabilityEntry(
        id: 'FLUTTER-STORAGE-001',
        title: 'Insecure SharedPreferences Usage',
        description: 'Storing sensitive data in SharedPreferences without encryption',
        severity: Severity.high,
        tags: ['flutter', 'storage', 'encryption'],
        source: 'Flutter Security Database',
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  /// Map OSV severity to our severity enum
  Severity _mapOSVSeverity(Map<String, dynamic> vuln) {
    final severity = vuln['database_specific']?['severity'];
    if (severity is String) {
      switch (severity.toLowerCase()) {
        case 'critical': return Severity.critical;
        case 'high': return Severity.high;
        case 'medium': return Severity.medium;
        case 'low': return Severity.low;
      }
    }
    return Severity.medium;
  }

  /// Ensure cache directory exists
  Future<void> _ensureCacheDir() async {
    final dir = Directory(_cacheDir);
    if (!await dir.exists()) {
      await dir.create(recursive: true);
    }
  }

  /// Load cached vulnerabilities
  Future<void> _loadCache() async {
    final cacheFile = File('$_cacheDir/vulnerabilities.json');
    if (await cacheFile.exists()) {
      try {
        final content = await cacheFile.readAsString();
        final data = jsonDecode(content) as Map<String, dynamic>;
        
        for (final entry in data.entries) {
          _cache[entry.key] = VulnerabilityEntry.fromJson(entry.value);
        }
      } catch (e) {
        print('Error loading cache: $e');
      }
    }
  }

  /// Save vulnerabilities to cache
  Future<void> _saveCache() async {
    final cacheFile = File('$_cacheDir/vulnerabilities.json');
    final data = <String, dynamic>{};
    
    for (final entry in _cache.entries) {
      data[entry.key] = entry.value.toJson();
    }

    await cacheFile.writeAsString(jsonEncode(data));
  }
}
