import 'package:analyzer/dart/ast/ast.dart';
import 'package:analyzer/dart/ast/visitor.dart';
import 'package:analyzer/source/line_info.dart';
import '../models.dart';

class FridaAnalyzer {
  /// Runs all Frida-related static checks on the AST
  static List<Finding> runAll(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    findings.addAll(_checkMethodChannelHooks(root, file, lineInfo));
    findings.addAll(_checkBadCertificate(root, file, lineInfo));
    findings.addAll(_checkPlatformChannelExposure(root, file, lineInfo));
    findings.addAll(_checkEvalDynamicCode(root, file, lineInfo));
    findings.addAll(_checkNativeInterop(root, file, lineInfo));
    findings.addAll(_checkAntiTamper(root, file, lineInfo));
    return findings;
  }

  /// Detect MethodChannel hooks that could be intercepted
  static List<Finding> _checkMethodChannelHooks(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitInstanceCreationExpression = (InstanceCreationExpression expr) {
        if (expr.constructorName.toSource().contains("MethodChannel")) {
          final loc = lineInfo.getLocation(expr.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "FRIDA001",
            message: "MethodChannel may be vulnerable to Frida hooking.",
            severity: Severity.high,
          ));
        }
      });
    return findings;
  }

  /// Detect badCertificateCallback
  static List<Finding> _checkBadCertificate(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitPropertyAccess = (PropertyAccess node) {
        if (node.toSource().contains("badCertificateCallback")) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "FRIDA002",
            message: "TLS insecure callback may be exploited by Frida.",
            severity: Severity.high,
          ));
        }
      });
    return findings;
  }

  /// Detect PlatformChannel exposure without sanitization
  static List<Finding> _checkPlatformChannelExposure(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation node) {
        if (node.toSource().contains("setMethodCallHandler")) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "FRIDA003",
            message: "PlatformChannel handler may be intercepted dynamically.",
            severity: Severity.high,
          ));
        }
      });
    return findings;
  }

  /// Detect dynamic code execution
  static List<Finding> _checkEvalDynamicCode(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation node) {
        if (node.methodName.name == "eval") {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "FRIDA004",
            message: "Dynamic code execution via eval could be hijacked.",
            severity: Severity.high,
          ));
        }
      });
    return findings;
  }

  /// Detect insecure native interop (optional)
  static List<Finding> _checkNativeInterop(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitInstanceCreationExpression = (InstanceCreationExpression expr) {
        if (expr.constructorName.toSource().contains("NativeCall") || 
            expr.constructorName.toSource().contains("ffi")) {
          final loc = lineInfo.getLocation(expr.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "FRIDA005",
            message: "Native FFI calls may be vulnerable to Frida/Xposed hooking.",
            severity: Severity.medium,
          ));
        }
      });
    return findings;
  }

  /// Detect missing anti-tamper / jailbreak detection
  static List<Finding> _checkAntiTamper(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    // Example: detect if no `rootBundle` / `Platform.isRooted` checks exist (stub)
    // In practice, would scan for presence of known anti-tamper methods
    // Placeholder: report if class extends FlutterActivity but no checks
    return findings;
  }
}
