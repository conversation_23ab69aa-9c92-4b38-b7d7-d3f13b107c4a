import 'dart:io';
import 'package:yaml/yaml.dart';
import '../models.dart';

class CveChecker {
  List<Finding> check(String lockPath) {
    final findings = <Finding>[];
    if (!File(lockPath).existsSync()) return findings;

    final yaml = loadYaml(File(lockPath).readAsStringSync());
    final pkgs = yaml["packages"] ?? {};

    pkgs.forEach((name, info) {
      final version = info["version"];
      // TODO: query OSV.dev API dynamically (stubbed here)
      if (name == "http" && version.startsWith("0.13.")) {
        findings.add(Finding(
          file: "pubspec.lock",
          line: 0,
          ruleId: "DEP001",
          message: "$name@$version has known issues (example).",
          severity: Severity.medium,
        ));
      }
    });
    return findings;
  }
}
