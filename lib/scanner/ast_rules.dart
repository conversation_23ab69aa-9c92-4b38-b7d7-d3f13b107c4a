import 'package:analyzer/dart/ast/ast.dart';
import 'package:analyzer/dart/ast/visitor.dart';
import 'package:analyzer/source/line_info.dart';
import '../models.dart';

class AstRules {
  static List<Finding> runAll(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    findings.addAll(_checkTls(root, file, lineInfo));
    findings.addAll(_checkStorage(root, file, lineInfo));
    findings.addAll(_checkMethodChannel(root, file, lineInfo));
    findings.addAll(_checkWebView(root, file, lineInfo));
    findings.addAll(_checkLogging(root, file, lineInfo));
    findings.addAll(_checkCrypto(root, file, lineInfo));
    return findings;
  }

  static List<Finding> _checkTls(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _TlsVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  static List<Finding> _checkStorage(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _StorageVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  static List<Finding> _checkMethodChannel(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _MethodChannelVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  static List<Finding> _checkWebView(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _WebViewVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  static List<Finding> _checkLogging(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _LoggingVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }

  static List<Finding> _checkCrypto(
      AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    final visitor = _CryptoVisitor(findings, file, lineInfo);
    root.accept(visitor);
    return findings;
  }
}

// Visitor classes for different security checks
class _TlsVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _TlsVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitMethodInvocation(MethodInvocation node) {
    if (node.toSource().contains("badCertificateCallback")) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "TLS001",
        message: "Insecure TLS: badCertificateCallback used.",
        severity: Severity.high,
      ));
    }
    super.visitMethodInvocation(node);
  }
}

class _StorageVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _StorageVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitMethodInvocation(MethodInvocation node) {
    if (node.methodName.name == "setString" &&
        node.argumentList.arguments.isNotEmpty &&
        node.argumentList.arguments.first.toSource().contains("password")) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "STO001",
        message: "Password stored in SharedPreferences.",
        severity: Severity.critical,
      ));
    }
    super.visitMethodInvocation(node);
  }
}

class _MethodChannelVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _MethodChannelVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitInstanceCreationExpression(InstanceCreationExpression node) {
    if (node.constructorName.toSource().contains("MethodChannel")) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "CHAN001",
        message: "Potentially unsafe MethodChannel usage.",
        severity: Severity.high,
      ));
    }
    super.visitInstanceCreationExpression(node);
  }
}

class _WebViewVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _WebViewVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitInstanceCreationExpression(InstanceCreationExpression node) {
    final source = node.toSource();
    if (source.contains("WebView") &&
        source.contains("JavascriptMode.unrestricted")) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "WEB001",
        message: "WebView allows unrestricted JS.",
        severity: Severity.medium,
      ));
    }
    super.visitInstanceCreationExpression(node);
  }
}

class _LoggingVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _LoggingVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitMethodInvocation(MethodInvocation node) {
    if (node.methodName.name == "print" &&
        node.argumentList.arguments.isNotEmpty &&
        node.argumentList.arguments.first.toSource().contains("password")) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "DBG001",
        message: "Sensitive info logged via print().",
        severity: Severity.medium,
      ));
    }
    super.visitMethodInvocation(node);
  }
}

class _CryptoVisitor extends RecursiveAstVisitor<void> {
  final List<Finding> findings;
  final String file;
  final LineInfo lineInfo;

  _CryptoVisitor(this.findings, this.file, this.lineInfo);

  @override
  void visitSimpleStringLiteral(SimpleStringLiteral node) {
    if (node.value.contains("AES/ECB")) {
      final loc = lineInfo.getLocation(node.offset);
      findings.add(Finding(
        file: file,
        line: loc.lineNumber,
        ruleId: "CRY001",
        message: "Insecure AES/ECB cipher detected.",
        severity: Severity.critical,
      ));
    }
    super.visitSimpleStringLiteral(node);
  }
}
