import 'package:analyzer/dart/ast/ast.dart';
import '../models.dart';

class AstRules {
  static List<Finding> runAll(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    findings.addAll(_checkTls(root, file, lineInfo));
    findings.addAll(_checkStorage(root, file, lineInfo));
    findings.addAll(_checkMethodChannel(root, file, lineInfo));
    findings.addAll(_checkWebView(root, file, lineInfo));
    findings.addAll(_checkLogging(root, file, lineInfo));
    findings.addAll(_checkCrypto(root, file, lineInfo));
    return findings;
  }

  static List<Finding> _checkTls(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation node) {
        if (node.toSource().contains("badCertificateCallback")) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "TLS001",
            message: "Insecure TLS: badCertificateCallback used.",
            severity: Severity.high,
          ));
        }
      });
    return findings;
  }

  static List<Finding> _checkStorage(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation node) {
        if (node.methodName.name == "setString" &&
            node.argumentList.arguments.first.toSource().contains("password")) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "STO001",
            message: "Password stored in SharedPreferences.",
            severity: Severity.critical,
          ));
        }
      });
    return findings;
  }

  static List<Finding> _checkMethodChannel(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitInstanceCreationExpression = (InstanceCreationExpression expr) {
        if (expr.constructorName.toSource().contains("MethodChannel")) {
          final loc = lineInfo.getLocation(expr.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "CHAN001",
            message: "Potentially unsafe MethodChannel usage.",
            severity: Severity.high,
          ));
        }
      });
    return findings;
  }

  static List<Finding> _checkWebView(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitInstanceCreationExpression = (InstanceCreationExpression expr) {
        if (expr.constructorName.type.name.name == "WebView" &&
            expr.argumentList.toSource().contains("JavascriptMode.unrestricted")) {
          final loc = lineInfo.getLocation(expr.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "WEB001",
            message: "WebView allows unrestricted JS.",
            severity: Severity.medium,
          ));
        }
      });
    return findings;
  }

  static List<Finding> _checkLogging(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitMethodInvocation = (MethodInvocation node) {
        if (node.methodName.name == "print" &&
            node.argumentList.arguments.first.toSource().contains("password")) {
          final loc = lineInfo.getLocation(node.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "DBG001",
            message: "Sensitive info logged via print().",
            severity: Severity.medium,
          ));
        }
      });
    return findings;
  }

  static List<Finding> _checkCrypto(AstNode root, String file, LineInfo lineInfo) {
    final findings = <Finding>[];
    root.visitChildren(SimpleAstVisitor()
      ..visitStringLiteral = (StringLiteral str) {
        if (str.stringValue != null && str.stringValue!.contains("AES/ECB")) {
          final loc = lineInfo.getLocation(str.offset);
          findings.add(Finding(
            file: file,
            line: loc.lineNumber,
            ruleId: "CRY001",
            message: "Insecure AES/ECB cipher detected.",
            severity: Severity.critical,
          ));
        }
      });
    return findings;
  }
}
