import 'dart:convert';
import 'dart:io';
import '../models.dart';

/// Frida script template for different vulnerability types
class FridaScript {
  final String name;
  final String description;
  final String script;
  final List<String> targetMethods;
  final Severity severity;

  FridaScript({
    required this.name,
    required this.description,
    required this.script,
    required this.targetMethods,
    required this.severity,
  });
}

/// Frida dynamic analysis integration
class FridaIntegration {
  final Map<String, FridaScript> _scripts = {};
  
  FridaIntegration() {
    _initializeScripts();
  }

  /// Initialize Frida scripts for different vulnerability types
  void _initializeScripts() {
    // Certificate Pinning Bypass Detection
    _scripts['cert_pinning'] = FridaScript(
      name: 'Certificate Pinning Bypass',
      description: 'Detects and attempts to bypass certificate pinning',
      script: _getCertPinningScript(),
      targetMethods: ['checkServerTrusted', 'verify', 'badCertificateCallback'],
      severity: Severity.critical,
    );

    // TLS/SSL Bypass Detection
    _scripts['tls_bypass'] = FridaScript(
      name: 'TLS/SSL Bypass',
      description: 'Detects TLS/SSL bypass vulnerabilities',
      script: _getTlsBypassScript(),
      targetMethods: ['SSLContext', 'TrustManager', 'HostnameVerifier'],
      severity: Severity.high,
    );

    // Method Channel Interception
    _scripts['method_channel'] = FridaScript(
      name: 'Method Channel Interception',
      description: 'Intercepts Flutter method channel communications',
      script: _getMethodChannelScript(),
      targetMethods: ['invokeMethod', 'setMethodCallHandler'],
      severity: Severity.high,
    );

    // Root/Jailbreak Detection Bypass
    _scripts['root_detection'] = FridaScript(
      name: 'Root Detection Bypass',
      description: 'Bypasses root/jailbreak detection mechanisms',
      script: _getRootDetectionScript(),
      targetMethods: ['isRooted', 'isJailbroken', 'checkRoot'],
      severity: Severity.medium,
    );

    // Memory Analysis
    _scripts['memory_analysis'] = FridaScript(
      name: 'Memory Analysis',
      description: 'Analyzes memory for sensitive data exposure',
      script: _getMemoryAnalysisScript(),
      targetMethods: ['malloc', 'free', 'memcpy'],
      severity: Severity.medium,
    );

    // Network Traffic Interception
    _scripts['network_intercept'] = FridaScript(
      name: 'Network Traffic Interception',
      description: 'Intercepts and analyzes network traffic',
      script: _getNetworkInterceptScript(),
      targetMethods: ['send', 'recv', 'connect'],
      severity: Severity.high,
    );
  }

  /// Generate Frida scripts for specific findings
  List<FridaScript> generateScriptsForFindings(List<Finding> findings) {
    final scripts = <FridaScript>[];
    final usedScripts = <String>{};

    for (final finding in findings) {
      final scriptKey = _getScriptKeyForFinding(finding);
      if (scriptKey != null && !usedScripts.contains(scriptKey)) {
        final script = _scripts[scriptKey];
        if (script != null) {
          scripts.add(script);
          usedScripts.add(scriptKey);
        }
      }
    }

    return scripts;
  }

  /// Get script key for a specific finding
  String? _getScriptKeyForFinding(Finding finding) {
    if (finding.ruleId.startsWith('TLS') || finding.message.contains('certificate')) {
      return 'cert_pinning';
    }
    if (finding.ruleId.startsWith('CHAN') || finding.message.contains('MethodChannel')) {
      return 'method_channel';
    }
    if (finding.ruleId.startsWith('ANTI') || finding.message.contains('root')) {
      return 'root_detection';
    }
    if (finding.ruleId.startsWith('NET') || finding.message.contains('network')) {
      return 'network_intercept';
    }
    if (finding.ruleId.startsWith('STO') || finding.message.contains('memory')) {
      return 'memory_analysis';
    }
    return null;
  }

  /// Execute Frida analysis
  Future<List<Finding>> executeAnalysis(String appPath, List<FridaScript> scripts) async {
    final findings = <Finding>[];
    
    for (final script in scripts) {
      try {
        final result = await _executeScript(appPath, script);
        findings.addAll(result);
      } catch (e) {
        print('Error executing Frida script ${script.name}: $e');
      }
    }

    return findings;
  }

  /// Execute a single Frida script
  Future<List<Finding>> _executeScript(String appPath, FridaScript script) async {
    // Create temporary script file
    final scriptFile = File('/tmp/frida_${script.name.replaceAll(' ', '_').toLowerCase()}.js');
    await scriptFile.writeAsString(script.script);

    try {
      // Execute Frida command
      final result = await Process.run('frida', [
        '-U',  // USB device
        '-f', appPath,  // Spawn app
        '-l', scriptFile.path,  // Load script
        '--no-pause',
      ]);

      // Parse results
      return _parseScriptOutput(result.stdout, script);
    } finally {
      // Clean up
      if (await scriptFile.exists()) {
        await scriptFile.delete();
      }
    }
  }

  /// Parse Frida script output
  List<Finding> _parseScriptOutput(String output, FridaScript script) {
    final findings = <Finding>[];
    final lines = output.split('\n');

    for (final line in lines) {
      if (line.contains('[VULNERABILITY]')) {
        final parts = line.split('|');
        if (parts.length >= 3) {
          findings.add(Finding(
            file: 'Runtime Analysis',
            line: 0,
            ruleId: 'FRIDA_${script.name.toUpperCase().replaceAll(' ', '_')}',
            message: parts[2].trim(),
            severity: script.severity,
            confirmed: true,
          ));
        }
      }
    }

    return findings;
  }

  /// Certificate Pinning Bypass Script
  String _getCertPinningScript() {
    return '''
Java.perform(function() {
    console.log("[INFO] Certificate Pinning Bypass Script Started");
    
    // Hook X509TrustManager
    var X509TrustManager = Java.use("javax.net.ssl.X509TrustManager");
    var SSLContext = Java.use("javax.net.ssl.SSLContext");
    
    // Bypass certificate validation
    X509TrustManager.checkServerTrusted.implementation = function(chain, authType) {
        console.log("[VULNERABILITY] | Certificate validation bypassed | " + authType);
        return;
    };
    
    // Hook Flutter's badCertificateCallback
    try {
        var HttpClient = Java.use("dart.io.HttpClient");
        HttpClient.badCertificateCallback.implementation = function(cert, host, port) {
            console.log("[VULNERABILITY] | Flutter badCertificateCallback detected | " + host + ":" + port);
            return true; // Always accept
        };
    } catch (e) {
        console.log("[INFO] Flutter HttpClient not found: " + e);
    }
    
    console.log("[INFO] Certificate Pinning Bypass Script Completed");
});
''';
  }

  /// TLS Bypass Script
  String _getTlsBypassScript() {
    return '''
Java.perform(function() {
    console.log("[INFO] TLS Bypass Script Started");
    
    // Hook SSLSocketFactory
    var SSLSocketFactory = Java.use("javax.net.ssl.SSLSocketFactory");
    var HttpsURLConnection = Java.use("javax.net.ssl.HttpsURLConnection");
    
    // Bypass hostname verification
    HttpsURLConnection.setDefaultHostnameVerifier.implementation = function(hostnameVerifier) {
        console.log("[VULNERABILITY] | Hostname verification bypassed");
        var TrustAllHostnameVerifier = Java.use("org.apache.http.conn.ssl.AllowAllHostnameVerifier");
        return this.setDefaultHostnameVerifier(TrustAllHostnameVerifier.\$new());
    };
    
    console.log("[INFO] TLS Bypass Script Completed");
});
''';
  }

  /// Method Channel Interception Script
  String _getMethodChannelScript() {
    return '''
Java.perform(function() {
    console.log("[INFO] Method Channel Interception Script Started");
    
    try {
        // Hook Flutter method channel
        var MethodChannel = Java.use("io.flutter.plugin.common.MethodChannel");
        
        MethodChannel.invokeMethod.overload('java.lang.String', 'java.lang.Object').implementation = function(method, arguments) {
            console.log("[VULNERABILITY] | Method channel call intercepted | Method: " + method + " Args: " + JSON.stringify(arguments));
            return this.invokeMethod(method, arguments);
        };
        
        // Hook method call handler
        MethodChannel.setMethodCallHandler.implementation = function(handler) {
            console.log("[VULNERABILITY] | Method call handler registered | Potential security risk");
            return this.setMethodCallHandler(handler);
        };
        
    } catch (e) {
        console.log("[INFO] Flutter MethodChannel not found: " + e);
    }
    
    console.log("[INFO] Method Channel Interception Script Completed");
});
''';
  }

  /// Root Detection Bypass Script
  String _getRootDetectionScript() {
    return '''
Java.perform(function() {
    console.log("[INFO] Root Detection Bypass Script Started");
    
    // Common root detection methods
    var File = Java.use("java.io.File");
    File.exists.implementation = function() {
        var path = this.getAbsolutePath();
        if (path.indexOf("su") !== -1 || path.indexOf("busybox") !== -1) {
            console.log("[VULNERABILITY] | Root detection bypassed for path: " + path);
            return false;
        }
        return this.exists();
    };
    
    // Hook Runtime.exec for su commands
    var Runtime = Java.use("java.lang.Runtime");
    Runtime.exec.overload('[Ljava.lang.String;').implementation = function(commands) {
        var cmdStr = commands.join(" ");
        if (cmdStr.indexOf("su") !== -1) {
            console.log("[VULNERABILITY] | Root command execution detected: " + cmdStr);
        }
        return this.exec(commands);
    };
    
    console.log("[INFO] Root Detection Bypass Script Completed");
});
''';
  }

  /// Memory Analysis Script
  String _getMemoryAnalysisScript() {
    return '''
Java.perform(function() {
    console.log("[INFO] Memory Analysis Script Started");
    
    // Hook String constructor to detect sensitive data
    var String = Java.use("java.lang.String");
    String.\$init.overload('[C').implementation = function(chars) {
        var str = this.\$init(chars);
        var content = str.toString();
        
        // Check for sensitive patterns
        if (content.match(/password|token|secret|key|api/i)) {
            console.log("[VULNERABILITY] | Sensitive data in memory | " + content.substring(0, 50) + "...");
        }
        
        return str;
    };
    
    console.log("[INFO] Memory Analysis Script Completed");
});
''';
  }

  /// Network Interception Script
  String _getNetworkInterceptScript() {
    return '''
Java.perform(function() {
    console.log("[INFO] Network Interception Script Started");
    
    // Hook URL connections
    var URL = Java.use("java.net.URL");
    URL.openConnection.overload().implementation = function() {
        var url = this.toString();
        if (url.startsWith("http://")) {
            console.log("[VULNERABILITY] | Insecure HTTP connection detected | " + url);
        }
        return this.openConnection();
    };
    
    // Hook OkHttp if present
    try {
        var OkHttpClient = Java.use("okhttp3.OkHttpClient");
        var Request = Java.use("okhttp3.Request");
        
        OkHttpClient.newCall.implementation = function(request) {
            var url = request.url().toString();
            console.log("[INFO] | Network request intercepted | " + url);
            return this.newCall(request);
        };
    } catch (e) {
        console.log("[INFO] OkHttp not found: " + e);
    }
    
    console.log("[INFO] Network Interception Script Completed");
});
''';
  }

  /// Check if Frida is available
  Future<bool> isFridaAvailable() async {
    try {
      final result = await Process.run('frida', ['--version']);
      return result.exitCode == 0;
    } catch (e) {
      return false;
    }
  }

  /// Get available devices
  Future<List<String>> getAvailableDevices() async {
    try {
      final result = await Process.run('frida-ls-devices', []);
      if (result.exitCode == 0) {
        return result.stdout.toString().split('\n')
            .where((line) => line.trim().isNotEmpty)
            .toList();
      }
    } catch (e) {
      print('Error getting Frida devices: $e');
    }
    return [];
  }

  /// Generate comprehensive Frida analysis report
  Map<String, dynamic> generateAnalysisReport(List<Finding> findings, List<FridaScript> scriptsUsed) {
    final report = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'scriptsExecuted': scriptsUsed.length,
      'vulnerabilitiesFound': findings.length,
      'scripts': scriptsUsed.map((s) => {
        'name': s.name,
        'description': s.description,
        'severity': s.severity.toString(),
      }).toList(),
      'findings': findings.map((f) => {
        'ruleId': f.ruleId,
        'message': f.message,
        'severity': f.severity.toString(),
        'confirmed': f.confirmed,
      }).toList(),
    };

    return report;
  }
}
