import 'dart:io';
import 'package:analyzer/dart/analysis/utilities.dart';
import 'scanner/ast_rules.dart';
import 'scanner/taint_engine.dart';
import 'scanner/config.dart';
import 'scanner/cve_checker.dart';
import 'scanner/reporter.dart';
import 'scanner/runtime_hooks.dart';
import 'models.dart';

class ScannerController {
  final Config config;
  final reporter = Reporter();
  final taint = TaintEngine();
  final runtime = RuntimeHooks();
  final cveChecker = CveChecker();

  ScannerController(this.config);

  Future<void> scan(String path) async {
    final findings = <Finding>[];

    // Scan Dart files
    final dartFiles = Directory(path)
        .listSync(recursive: true)
        .where((f) => f.path.endsWith(".dart"));
    for (var f in dartFiles) {
      final content = File(f.path).readAsStringSync();
      final result = parseString(content: content, throwIfDiagnostics: false);
      final root = result.unit;
      final lineInfo = result.lineInfo;

      findings.addAll(AstRules.runAll(root, f.path, lineInfo));
      findings.addAll(taint.analyze(root, f.path, lineInfo));
    }

    // Dependency checks
    findings.addAll(cveChecker.check("$path/pubspec.lock"));

    // Apply suppressions
    final filtered = findings
        .where((f) => !config.isSuppressed(f.file, f.ruleId))
        .toList();

    // Runtime verification
    final confirmed = await runtime.verify(filtered);

    // Report
    reporter.printFindings(confirmed);
  }
}
