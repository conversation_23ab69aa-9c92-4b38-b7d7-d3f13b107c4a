import 'dart:async';
import 'dart:convert';
import 'dart:io';
import '../models.dart';
import '../scanner/vulnerability_database.dart';
import '../scanner/frida_integration.dart';
import 'security_agent.dart';

/// LangGraph-inspired workflow node
abstract class WorkflowNode {
  String get name;
  Future<WorkflowResult> execute(WorkflowContext context);
}

/// Workflow execution result
class WorkflowResult {
  final bool success;
  final WorkflowContext updatedContext;
  final String? nextNode;
  final String? message;
  final Map<String, dynamic>? data;

  WorkflowResult({
    required this.success,
    required this.updatedContext,
    this.nextNode,
    this.message,
    this.data,
  });
}

/// Workflow execution context
class WorkflowContext {
  final String projectPath;
  final Map<String, dynamic> state;
  final List<Finding> findings;
  final List<VulnerabilityEntry> vulnerabilities;
  final DateTime startTime;

  WorkflowContext({
    required this.projectPath,
    this.state = const {},
    this.findings = const [],
    this.vulnerabilities = const [],
    DateTime? startTime,
  }) : startTime = startTime ?? DateTime.now();

  WorkflowContext copyWith({
    String? projectPath,
    Map<String, dynamic>? state,
    List<Finding>? findings,
    List<VulnerabilityEntry>? vulnerabilities,
    DateTime? startTime,
  }) {
    return WorkflowContext(
      projectPath: projectPath ?? this.projectPath,
      state: state ?? Map<String, dynamic>.from(this.state),
      findings: findings ?? List<Finding>.from(this.findings),
      vulnerabilities: vulnerabilities ?? List<VulnerabilityEntry>.from(this.vulnerabilities),
      startTime: startTime ?? this.startTime,
    );
  }
}

/// LangGraph-inspired workflow engine for Flutter security analysis
class SecurityWorkflowEngine {
  final Map<String, WorkflowNode> _nodes = {};
  final Map<String, List<String>> _edges = {};
  final VulnerabilityDatabase _vulnDb;
  final FridaIntegration _frida;

  SecurityWorkflowEngine(this._vulnDb, this._frida) {
    _initializeWorkflow();
  }

  /// Initialize the workflow graph
  void _initializeWorkflow() {
    // Register nodes
    _nodes['start'] = StartNode();
    _nodes['code_discovery'] = CodeDiscoveryNode();
    _nodes['static_analysis'] = StaticAnalysisNode();
    _nodes['vulnerability_lookup'] = VulnerabilityLookupNode(_vulnDb);
    _nodes['dynamic_analysis_planning'] = DynamicAnalysisPlanningNode(_frida);
    _nodes['frida_execution'] = FridaExecutionNode(_frida);
    _nodes['risk_assessment'] = RiskAssessmentNode();
    _nodes['remediation_planning'] = RemediationPlanningNode();
    _nodes['report_generation'] = ReportGenerationNode();
    _nodes['end'] = EndNode();

    // Define workflow edges (state transitions)
    _edges['start'] = ['code_discovery'];
    _edges['code_discovery'] = ['static_analysis'];
    _edges['static_analysis'] = ['vulnerability_lookup'];
    _edges['vulnerability_lookup'] = ['dynamic_analysis_planning'];
    _edges['dynamic_analysis_planning'] = ['frida_execution', 'risk_assessment']; // Conditional
    _edges['frida_execution'] = ['risk_assessment'];
    _edges['risk_assessment'] = ['remediation_planning'];
    _edges['remediation_planning'] = ['report_generation'];
    _edges['report_generation'] = ['end'];
  }

  /// Execute the complete workflow
  Future<SecurityAnalysisReport> execute(String projectPath) async {
    var context = WorkflowContext(projectPath: projectPath);
    var currentNode = 'start';

    print('🚀 Starting LangGraph-based security analysis workflow');
    print('📁 Project: $projectPath');

    while (currentNode != 'end') {
      final node = _nodes[currentNode];
      if (node == null) {
        throw Exception('Unknown node: $currentNode');
      }

      print('🔄 Executing node: ${node.name}');

      try {
        final result = await node.execute(context);
        context = result.updatedContext;

        if (result.message != null) {
          print('💬 ${result.message}');
        }

        if (!result.success) {
          throw Exception('Node ${node.name} failed');
        }

        // Determine next node
        currentNode = result.nextNode ?? _getNextNode(currentNode, context);

      } catch (e) {
        print('❌ Error in node ${node.name}: $e');
        rethrow;
      }
    }

    print('✅ Workflow completed successfully');
    return _generateReport(context);
  }

  /// Get next node based on current node and context
  String _getNextNode(String currentNode, WorkflowContext context) {
    final possibleNext = _edges[currentNode] ?? [];
    
    if (possibleNext.isEmpty) {
      return 'end';
    }

    // Handle conditional routing
    if (currentNode == 'dynamic_analysis_planning') {
      final shouldRunFrida = context.state['shouldRunFrida'] ?? false;
      return shouldRunFrida ? 'frida_execution' : 'risk_assessment';
    }

    return possibleNext.first;
  }

  /// Generate final report
  SecurityAnalysisReport _generateReport(WorkflowContext context) {
    return SecurityAnalysisReport(
      projectPath: context.projectPath,
      analysisDate: DateTime.now(),
      findings: context.findings,
      vulnerabilities: context.vulnerabilities,
      riskScore: context.state['riskScore'] ?? 0.0,
      riskLevel: context.state['riskLevel'] ?? 'UNKNOWN',
      remediations: List<Map<String, dynamic>>.from(context.state['remediations'] ?? []),
      metadata: {
        ...context.state,
        'executionTime': DateTime.now().difference(context.startTime).inMilliseconds,
        'workflowVersion': '1.0.0',
      },
    );
  }
}

/// Start node
class StartNode extends WorkflowNode {
  @override
  String get name => 'Start';

  @override
  Future<WorkflowResult> execute(WorkflowContext context) async {
    final updatedState = Map<String, dynamic>.from(context.state);
    updatedState['startTime'] = DateTime.now().toIso8601String();
    updatedState['workflowId'] = 'security_analysis_${DateTime.now().millisecondsSinceEpoch}';

    return WorkflowResult(
      success: true,
      updatedContext: context.copyWith(state: updatedState),
      message: 'Security analysis workflow initialized',
    );
  }
}

/// Code discovery node
class CodeDiscoveryNode extends WorkflowNode {
  @override
  String get name => 'Code Discovery';

  @override
  Future<WorkflowResult> execute(WorkflowContext context) async {
    final dartFiles = await _discoverDartFiles(context.projectPath);
    final configFiles = await _discoverConfigFiles(context.projectPath);

    final updatedState = Map<String, dynamic>.from(context.state);
    updatedState['dartFiles'] = dartFiles;
    updatedState['configFiles'] = configFiles;
    updatedState['totalFiles'] = dartFiles.length + configFiles.length;

    return WorkflowResult(
      success: true,
      updatedContext: context.copyWith(state: updatedState),
      message: 'Discovered ${dartFiles.length} Dart files and ${configFiles.length} config files',
    );
  }

  Future<List<String>> _discoverDartFiles(String projectPath) async {
    final files = <String>[];
    final dir = Directory(projectPath);
    
    if (await dir.exists()) {
      await for (final entity in dir.list(recursive: true)) {
        if (entity is File && entity.path.endsWith('.dart')) {
          files.add(entity.path);
        }
      }
    }
    
    return files;
  }

  Future<List<String>> _discoverConfigFiles(String projectPath) async {
    final files = <String>[];
    final configPatterns = ['pubspec.yaml', 'android/app/build.gradle', 'ios/Runner/Info.plist'];
    
    for (final pattern in configPatterns) {
      final file = File('$projectPath/$pattern');
      if (await file.exists()) {
        files.add(file.path);
      }
    }
    
    return files;
  }
}

/// Static analysis node
class StaticAnalysisNode extends WorkflowNode {
  @override
  String get name => 'Static Analysis';

  @override
  Future<WorkflowResult> execute(WorkflowContext context) async {
    final dartFiles = List<String>.from(context.state['dartFiles'] ?? []);
    final findings = <Finding>[];

    // Analyze each Dart file
    for (final filePath in dartFiles) {
      try {
        final fileFindings = await _analyzeFile(filePath);
        findings.addAll(fileFindings);
      } catch (e) {
        print('Warning: Failed to analyze $filePath: $e');
      }
    }

    final updatedState = Map<String, dynamic>.from(context.state);
    updatedState['staticAnalysisComplete'] = true;
    updatedState['findingsCount'] = findings.length;

    return WorkflowResult(
      success: true,
      updatedContext: context.copyWith(
        state: updatedState,
        findings: findings,
      ),
      message: 'Static analysis completed: ${findings.length} findings',
    );
  }

  Future<List<Finding>> _analyzeFile(String filePath) async {
    // This would implement actual static analysis
    // For now, return placeholder findings based on file content
    final file = File(filePath);
    final content = await file.readAsString();
    final findings = <Finding>[];

    // Simple pattern matching for demonstration
    if (content.contains('badCertificateCallback')) {
      findings.add(Finding(
        file: filePath,
        line: 1,
        ruleId: 'TLS001',
        message: 'Insecure certificate validation detected',
        severity: Severity.critical,
      ));
    }

    if (content.contains('SharedPreferences') && content.contains('password')) {
      findings.add(Finding(
        file: filePath,
        line: 1,
        ruleId: 'STO001',
        message: 'Sensitive data in SharedPreferences',
        severity: Severity.high,
      ));
    }

    return findings;
  }
}

/// Vulnerability lookup node
class VulnerabilityLookupNode extends WorkflowNode {
  final VulnerabilityDatabase _vulnDb;

  VulnerabilityLookupNode(this._vulnDb);

  @override
  String get name => 'Vulnerability Lookup';

  @override
  Future<WorkflowResult> execute(WorkflowContext context) async {
    final vulnerabilities = <VulnerabilityEntry>[];

    // Query vulnerability database for each finding
    for (final finding in context.findings) {
      final query = '${finding.ruleId} ${finding.message}';
      final vulns = await _vulnDb.query(query, limit: 3);
      vulnerabilities.addAll(vulns);
    }

    // Remove duplicates
    final uniqueVulns = <String, VulnerabilityEntry>{};
    for (final vuln in vulnerabilities) {
      uniqueVulns[vuln.id] = vuln;
    }

    final updatedState = Map<String, dynamic>.from(context.state);
    updatedState['vulnerabilityLookupComplete'] = true;
    updatedState['vulnerabilitiesFound'] = uniqueVulns.length;

    return WorkflowResult(
      success: true,
      updatedContext: context.copyWith(
        state: updatedState,
        vulnerabilities: uniqueVulns.values.toList(),
      ),
      message: 'Found ${uniqueVulns.length} related vulnerabilities',
    );
  }
}

/// Dynamic analysis planning node
class DynamicAnalysisPlanningNode extends WorkflowNode {
  final FridaIntegration _frida;

  DynamicAnalysisPlanningNode(this._frida);

  @override
  String get name => 'Dynamic Analysis Planning';

  @override
  Future<WorkflowResult> execute(WorkflowContext context) async {
    final shouldRunFrida = await _frida.isFridaAvailable();
    final scripts = _frida.generateScriptsForFindings(context.findings);

    final updatedState = Map<String, dynamic>.from(context.state);
    updatedState['shouldRunFrida'] = shouldRunFrida;
    updatedState['fridaScripts'] = scripts.map((s) => s.name).toList();
    updatedState['dynamicAnalysisPlanned'] = true;

    return WorkflowResult(
      success: true,
      updatedContext: context.copyWith(state: updatedState),
      message: shouldRunFrida 
          ? 'Planned ${scripts.length} Frida scripts for dynamic analysis'
          : 'Frida not available, skipping dynamic analysis',
    );
  }
}

/// Frida execution node
class FridaExecutionNode extends WorkflowNode {
  final FridaIntegration _frida;

  FridaExecutionNode(this._frida);

  @override
  String get name => 'Frida Execution';

  @override
  Future<WorkflowResult> execute(WorkflowContext context) async {
    final scripts = _frida.generateScriptsForFindings(context.findings);
    final dynamicFindings = <Finding>[];

    // Note: In a real implementation, this would require an actual app to analyze
    // For demonstration, we'll simulate the execution
    for (final script in scripts) {
      // Simulate finding vulnerabilities
      dynamicFindings.add(Finding(
        file: 'Runtime Analysis',
        line: 0,
        ruleId: 'FRIDA_${script.name.toUpperCase().replaceAll(' ', '_')}',
        message: 'Dynamic analysis detected: ${script.description}',
        severity: script.severity,
        confirmed: true,
      ));
    }

    final allFindings = [...context.findings, ...dynamicFindings];
    final updatedState = Map<String, dynamic>.from(context.state);
    updatedState['fridaExecutionComplete'] = true;
    updatedState['dynamicFindingsCount'] = dynamicFindings.length;

    return WorkflowResult(
      success: true,
      updatedContext: context.copyWith(
        state: updatedState,
        findings: allFindings,
      ),
      message: 'Dynamic analysis completed: ${dynamicFindings.length} additional findings',
    );
  }
}

/// Risk assessment node
class RiskAssessmentNode extends WorkflowNode {
  @override
  String get name => 'Risk Assessment';

  @override
  Future<WorkflowResult> execute(WorkflowContext context) async {
    final riskScore = _calculateRiskScore(context.findings, context.vulnerabilities);
    final riskLevel = _getRiskLevel(riskScore);

    final updatedState = Map<String, dynamic>.from(context.state);
    updatedState['riskScore'] = riskScore;
    updatedState['riskLevel'] = riskLevel;
    updatedState['riskAssessmentComplete'] = true;

    return WorkflowResult(
      success: true,
      updatedContext: context.copyWith(state: updatedState),
      message: 'Risk assessment: $riskLevel (score: ${riskScore.toStringAsFixed(1)})',
    );
  }

  double _calculateRiskScore(List<Finding> findings, List<VulnerabilityEntry> vulnerabilities) {
    double score = 0.0;
    
    for (final finding in findings) {
      switch (finding.severity) {
        case Severity.critical:
          score += finding.confirmed ? 15.0 : 10.0;
          break;
        case Severity.high:
          score += finding.confirmed ? 10.0 : 7.0;
          break;
        case Severity.medium:
          score += finding.confirmed ? 6.0 : 4.0;
          break;
        case Severity.low:
          score += finding.confirmed ? 2.0 : 1.0;
          break;
      }
    }

    // Add vulnerability context
    for (final vuln in vulnerabilities) {
      switch (vuln.severity) {
        case Severity.critical:
          score += 5.0;
          break;
        case Severity.high:
          score += 3.0;
          break;
        case Severity.medium:
          score += 2.0;
          break;
        case Severity.low:
          score += 0.5;
          break;
      }
    }

    return score;
  }

  String _getRiskLevel(double score) {
    if (score >= 50) return 'CRITICAL';
    if (score >= 30) return 'HIGH';
    if (score >= 15) return 'MEDIUM';
    return 'LOW';
  }
}

/// Remediation planning node
class RemediationPlanningNode extends WorkflowNode {
  @override
  String get name => 'Remediation Planning';

  @override
  Future<WorkflowResult> execute(WorkflowContext context) async {
    final remediations = _generateRemediations(context.findings);

    final updatedState = Map<String, dynamic>.from(context.state);
    updatedState['remediations'] = remediations;
    updatedState['remediationPlanningComplete'] = true;

    return WorkflowResult(
      success: true,
      updatedContext: context.copyWith(state: updatedState),
      message: 'Generated ${remediations.length} remediation recommendations',
    );
  }

  List<Map<String, dynamic>> _generateRemediations(List<Finding> findings) {
    final remediations = <Map<String, dynamic>>[];
    
    for (final finding in findings) {
      remediations.add({
        'finding': finding.ruleId,
        'file': finding.file,
        'line': finding.line,
        'severity': finding.severity.toString(),
        'recommendation': _getRemediation(finding),
        'effort': _getEffortEstimate(finding),
        'priority': _getPriority(finding),
      });
    }

    // Sort by priority
    remediations.sort((a, b) => _comparePriority(a['priority'], b['priority']));

    return remediations;
  }

  String _getRemediation(Finding finding) {
    switch (finding.ruleId) {
      case 'TLS001':
        return 'Implement proper certificate validation. Remove badCertificateCallback or implement proper certificate pinning.';
      case 'STO001':
        return 'Use encrypted storage solutions like flutter_secure_storage instead of SharedPreferences for sensitive data.';
      case 'CHAN001':
        return 'Implement input validation and access controls for MethodChannel communications.';
      case 'WEB001':
        return 'Restrict JavaScript execution in WebView and implement proper content security policies.';
      default:
        return 'Review and address the identified security issue according to security best practices.';
    }
  }

  String _getEffortEstimate(Finding finding) {
    switch (finding.severity) {
      case Severity.critical:
        return 'HIGH';
      case Severity.high:
        return 'MEDIUM';
      case Severity.medium:
        return 'LOW';
      case Severity.low:
        return 'MINIMAL';
    }
  }

  int _getPriority(Finding finding) {
    // Priority based on severity and confirmation
    int basePriority = switch (finding.severity) {
      Severity.critical => 1,
      Severity.high => 2,
      Severity.medium => 3,
      Severity.low => 4,
    };

    // Confirmed findings get higher priority
    return finding.confirmed ? basePriority : basePriority + 4;
  }

  int _comparePriority(int a, int b) => a.compareTo(b);
}

/// Report generation node
class ReportGenerationNode extends WorkflowNode {
  @override
  String get name => 'Report Generation';

  @override
  Future<WorkflowResult> execute(WorkflowContext context) async {
    final reportPath = await _generateReport(context);

    final updatedState = Map<String, dynamic>.from(context.state);
    updatedState['reportPath'] = reportPath;
    updatedState['reportGenerationComplete'] = true;

    return WorkflowResult(
      success: true,
      updatedContext: context.copyWith(state: updatedState),
      message: 'Security report generated: $reportPath',
    );
  }

  Future<String> _generateReport(WorkflowContext context) async {
    final report = {
      'metadata': {
        'projectPath': context.projectPath,
        'analysisDate': DateTime.now().toIso8601String(),
        'workflowId': context.state['workflowId'],
        'executionTime': DateTime.now().difference(context.startTime).inMilliseconds,
      },
      'summary': {
        'totalFindings': context.findings.length,
        'totalVulnerabilities': context.vulnerabilities.length,
        'riskScore': context.state['riskScore'],
        'riskLevel': context.state['riskLevel'],
      },
      'findings': context.findings.map((f) => {
        'file': f.file,
        'line': f.line,
        'ruleId': f.ruleId,
        'message': f.message,
        'severity': f.severity.toString(),
        'confirmed': f.confirmed,
      }).toList(),
      'vulnerabilities': context.vulnerabilities.map((v) => v.toJson()).toList(),
      'remediations': context.state['remediations'] ?? [],
    };

    final reportPath = '${context.projectPath}/security_report_${DateTime.now().millisecondsSinceEpoch}.json';
    final reportFile = File(reportPath);
    await reportFile.writeAsString(jsonEncode(report));

    return reportPath;
  }
}

/// End node
class EndNode extends WorkflowNode {
  @override
  String get name => 'End';

  @override
  Future<WorkflowResult> execute(WorkflowContext context) async {
    return WorkflowResult(
      success: true,
      updatedContext: context,
      message: 'Security analysis workflow completed successfully',
    );
  }
}
