import 'dart:async';
import 'dart:convert';
import '../models.dart';
import '../scanner/vulnerability_database.dart';
import '../scanner/ast_rules.dart';
import '../scanner/frida_analyzer.dart';
import '../scanner/taint_engine.dart';

/// Agent state for the security analysis workflow
enum AgentState {
  initializing,
  codeDiscovery,
  staticAnalysis,
  dynamicAnalysisPlanning,
  vulnerabilityLookup,
  riskAssessment,
  remediationPlanning,
  reportGeneration,
  completed,
  error,
}

/// Context for the security analysis
class AnalysisContext {
  final String projectPath;
  final List<String> dartFiles;
  final List<Finding> findings;
  final List<VulnerabilityEntry> vulnerabilities;
  final Map<String, dynamic> metadata;
  AgentState currentState;
  String? errorMessage;

  AnalysisContext({
    required this.projectPath,
    this.dartFiles = const [],
    this.findings = const [],
    this.vulnerabilities = const [],
    this.metadata = const {},
    this.currentState = AgentState.initializing,
    this.errorMessage,
  });

  AnalysisContext copyWith({
    String? projectPath,
    List<String>? dartFiles,
    List<Finding>? findings,
    List<VulnerabilityEntry>? vulnerabilities,
    Map<String, dynamic>? metadata,
    AgentState? currentState,
    String? errorMessage,
  }) {
    return AnalysisContext(
      projectPath: projectPath ?? this.projectPath,
      dartFiles: dartFiles ?? this.dartFiles,
      findings: findings ?? this.findings,
      vulnerabilities: vulnerabilities ?? this.vulnerabilities,
      metadata: metadata ?? this.metadata,
      currentState: currentState ?? this.currentState,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// Agent action result
class AgentAction {
  final AgentState nextState;
  final AnalysisContext updatedContext;
  final String? message;

  AgentAction({
    required this.nextState,
    required this.updatedContext,
    this.message,
  });
}

/// Main security analysis agent
class SecurityAgent {
  final VulnerabilityDatabase _vulnDb;
  final TaintEngine _taintEngine;
  final Map<AgentState, Function(AnalysisContext)> _stateHandlers = {};

  SecurityAgent(this._vulnDb) : _taintEngine = TaintEngine() {
    _initializeStateHandlers();
  }

  /// Initialize the agent
  Future<void> initialize() async {
    await _vulnDb.initialize();
  }

  /// Run the complete security analysis workflow
  Future<SecurityAnalysisReport> analyze(String projectPath) async {
    var context = AnalysisContext(projectPath: projectPath);
    
    print('🤖 Starting AI-powered security analysis for: $projectPath');
    
    while (context.currentState != AgentState.completed && 
           context.currentState != AgentState.error) {
      
      print('📍 Current state: ${context.currentState}');
      
      try {
        final action = await _executeState(context);
        context = action.updatedContext;
        
        if (action.message != null) {
          print('💬 ${action.message}');
        }
        
        // Add small delay to make the process observable
        await Future.delayed(const Duration(milliseconds: 100));
        
      } catch (e) {
        print('❌ Error in state ${context.currentState}: $e');
        context = context.copyWith(
          currentState: AgentState.error,
          errorMessage: e.toString(),
        );
      }
    }

    return _generateFinalReport(context);
  }

  /// Execute the current state
  Future<AgentAction> _executeState(AnalysisContext context) async {
    final handler = _stateHandlers[context.currentState];
    if (handler == null) {
      throw Exception('No handler for state: ${context.currentState}');
    }
    
    return await handler(context);
  }

  /// Initialize state handlers
  void _initializeStateHandlers() {
    _stateHandlers[AgentState.initializing] = _handleInitializing;
    _stateHandlers[AgentState.codeDiscovery] = _handleCodeDiscovery;
    _stateHandlers[AgentState.staticAnalysis] = _handleStaticAnalysis;
    _stateHandlers[AgentState.dynamicAnalysisPlanning] = _handleDynamicAnalysisPlanning;
    _stateHandlers[AgentState.vulnerabilityLookup] = _handleVulnerabilityLookup;
    _stateHandlers[AgentState.riskAssessment] = _handleRiskAssessment;
    _stateHandlers[AgentState.remediationPlanning] = _handleRemediationPlanning;
    _stateHandlers[AgentState.reportGeneration] = _handleReportGeneration;
  }

  /// Handle initialization state
  Future<AgentAction> _handleInitializing(AnalysisContext context) async {
    return AgentAction(
      nextState: AgentState.codeDiscovery,
      updatedContext: context.copyWith(currentState: AgentState.codeDiscovery),
      message: 'Agent initialized, starting code discovery...',
    );
  }

  /// Handle code discovery state
  Future<AgentAction> _handleCodeDiscovery(AnalysisContext context) async {
    final dartFiles = await _discoverDartFiles(context.projectPath);
    
    return AgentAction(
      nextState: AgentState.staticAnalysis,
      updatedContext: context.copyWith(
        dartFiles: dartFiles,
        currentState: AgentState.staticAnalysis,
      ),
      message: 'Discovered ${dartFiles.length} Dart files for analysis',
    );
  }

  /// Handle static analysis state
  Future<AgentAction> _handleStaticAnalysis(AnalysisContext context) async {
    final findings = <Finding>[];
    
    for (final filePath in context.dartFiles) {
      try {
        final fileFindings = await _analyzeFile(filePath);
        findings.addAll(fileFindings);
      } catch (e) {
        print('Warning: Failed to analyze $filePath: $e');
      }
    }

    return AgentAction(
      nextState: AgentState.dynamicAnalysisPlanning,
      updatedContext: context.copyWith(
        findings: findings,
        currentState: AgentState.dynamicAnalysisPlanning,
      ),
      message: 'Static analysis completed, found ${findings.length} potential issues',
    );
  }

  /// Handle dynamic analysis planning state
  Future<AgentAction> _handleDynamicAnalysisPlanning(AnalysisContext context) async {
    // Plan dynamic analysis based on static findings
    final fridaTargets = <String>[];
    
    for (final finding in context.findings) {
      if (finding.ruleId.startsWith('FRIDA') || 
          finding.ruleId.startsWith('CHAN') ||
          finding.ruleId.startsWith('TLS')) {
        fridaTargets.add(finding.file);
      }
    }

    final metadata = Map<String, dynamic>.from(context.metadata);
    metadata['fridaTargets'] = fridaTargets;

    return AgentAction(
      nextState: AgentState.vulnerabilityLookup,
      updatedContext: context.copyWith(
        metadata: metadata,
        currentState: AgentState.vulnerabilityLookup,
      ),
      message: 'Planned dynamic analysis for ${fridaTargets.length} files',
    );
  }

  /// Handle vulnerability lookup state
  Future<AgentAction> _handleVulnerabilityLookup(AnalysisContext context) async {
    final vulnerabilities = <VulnerabilityEntry>[];
    
    // Query vulnerability database for each finding
    for (final finding in context.findings) {
      final query = '${finding.ruleId} ${finding.message}';
      final vulns = await _vulnDb.query(query, limit: 3);
      vulnerabilities.addAll(vulns);
    }

    // Remove duplicates
    final uniqueVulns = <String, VulnerabilityEntry>{};
    for (final vuln in vulnerabilities) {
      uniqueVulns[vuln.id] = vuln;
    }

    return AgentAction(
      nextState: AgentState.riskAssessment,
      updatedContext: context.copyWith(
        vulnerabilities: uniqueVulns.values.toList(),
        currentState: AgentState.riskAssessment,
      ),
      message: 'Found ${uniqueVulns.length} related vulnerabilities in database',
    );
  }

  /// Handle risk assessment state
  Future<AgentAction> _handleRiskAssessment(AnalysisContext context) async {
    final riskScore = _calculateRiskScore(context.findings, context.vulnerabilities);
    final metadata = Map<String, dynamic>.from(context.metadata);
    metadata['riskScore'] = riskScore;
    metadata['riskLevel'] = _getRiskLevel(riskScore);

    return AgentAction(
      nextState: AgentState.remediationPlanning,
      updatedContext: context.copyWith(
        metadata: metadata,
        currentState: AgentState.remediationPlanning,
      ),
      message: 'Risk assessment completed: ${metadata['riskLevel']} (score: $riskScore)',
    );
  }

  /// Handle remediation planning state
  Future<AgentAction> _handleRemediationPlanning(AnalysisContext context) async {
    final remediations = _generateRemediations(context.findings, context.vulnerabilities);
    final metadata = Map<String, dynamic>.from(context.metadata);
    metadata['remediations'] = remediations;

    return AgentAction(
      nextState: AgentState.reportGeneration,
      updatedContext: context.copyWith(
        metadata: metadata,
        currentState: AgentState.reportGeneration,
      ),
      message: 'Generated ${remediations.length} remediation recommendations',
    );
  }

  /// Handle report generation state
  Future<AgentAction> _handleReportGeneration(AnalysisContext context) async {
    return AgentAction(
      nextState: AgentState.completed,
      updatedContext: context.copyWith(currentState: AgentState.completed),
      message: 'Security analysis report generated successfully',
    );
  }

  /// Discover Dart files in the project
  Future<List<String>> _discoverDartFiles(String projectPath) async {
    // Implementation would scan for .dart files
    // For now, return a placeholder
    return ['lib/main.dart', 'lib/models.dart'];
  }

  /// Analyze a single file
  Future<List<Finding>> _analyzeFile(String filePath) async {
    // This would implement the actual file analysis
    // For now, return placeholder findings
    return [];
  }

  /// Calculate overall risk score
  double _calculateRiskScore(List<Finding> findings, List<VulnerabilityEntry> vulnerabilities) {
    double score = 0.0;
    
    for (final finding in findings) {
      switch (finding.severity) {
        case Severity.critical:
          score += 10.0;
          break;
        case Severity.high:
          score += 7.0;
          break;
        case Severity.medium:
          score += 4.0;
          break;
        case Severity.low:
          score += 1.0;
          break;
      }
    }

    // Add vulnerability context
    for (final vuln in vulnerabilities) {
      switch (vuln.severity) {
        case Severity.critical:
          score += 5.0;
          break;
        case Severity.high:
          score += 3.0;
          break;
        case Severity.medium:
          score += 2.0;
          break;
        case Severity.low:
          score += 0.5;
          break;
      }
    }

    return score;
  }

  /// Get risk level from score
  String _getRiskLevel(double score) {
    if (score >= 50) return 'CRITICAL';
    if (score >= 30) return 'HIGH';
    if (score >= 15) return 'MEDIUM';
    return 'LOW';
  }

  /// Generate remediation recommendations
  List<Map<String, dynamic>> _generateRemediations(
    List<Finding> findings, 
    List<VulnerabilityEntry> vulnerabilities
  ) {
    final remediations = <Map<String, dynamic>>[];
    
    for (final finding in findings) {
      remediations.add({
        'finding': finding.ruleId,
        'priority': finding.severity.toString(),
        'recommendation': _getRemediation(finding),
        'effort': _getEffortEstimate(finding),
      });
    }

    return remediations;
  }

  /// Get remediation for a finding
  String _getRemediation(Finding finding) {
    // This would contain a comprehensive remediation database
    switch (finding.ruleId) {
      case 'TLS001':
        return 'Implement proper certificate validation and avoid badCertificateCallback';
      case 'STO001':
        return 'Use encrypted storage solutions like flutter_secure_storage';
      case 'CHAN001':
        return 'Implement input validation and access controls for MethodChannel';
      default:
        return 'Review and fix the identified security issue';
    }
  }

  /// Get effort estimate for fixing a finding
  String _getEffortEstimate(Finding finding) {
    switch (finding.severity) {
      case Severity.critical:
        return 'HIGH';
      case Severity.high:
        return 'MEDIUM';
      case Severity.medium:
        return 'LOW';
      case Severity.low:
        return 'MINIMAL';
    }
  }

  /// Generate final security analysis report
  SecurityAnalysisReport _generateFinalReport(AnalysisContext context) {
    return SecurityAnalysisReport(
      projectPath: context.projectPath,
      analysisDate: DateTime.now(),
      findings: context.findings,
      vulnerabilities: context.vulnerabilities,
      riskScore: context.metadata['riskScore'] ?? 0.0,
      riskLevel: context.metadata['riskLevel'] ?? 'UNKNOWN',
      remediations: List<Map<String, dynamic>>.from(context.metadata['remediations'] ?? []),
      metadata: context.metadata,
    );
  }
}

/// Security analysis report
class SecurityAnalysisReport {
  final String projectPath;
  final DateTime analysisDate;
  final List<Finding> findings;
  final List<VulnerabilityEntry> vulnerabilities;
  final double riskScore;
  final String riskLevel;
  final List<Map<String, dynamic>> remediations;
  final Map<String, dynamic> metadata;

  SecurityAnalysisReport({
    required this.projectPath,
    required this.analysisDate,
    required this.findings,
    required this.vulnerabilities,
    required this.riskScore,
    required this.riskLevel,
    required this.remediations,
    required this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'projectPath': projectPath,
      'analysisDate': analysisDate.toIso8601String(),
      'findings': findings.map((f) => {
        'file': f.file,
        'line': f.line,
        'ruleId': f.ruleId,
        'message': f.message,
        'severity': f.severity.toString(),
        'confirmed': f.confirmed,
      }).toList(),
      'vulnerabilities': vulnerabilities.map((v) => v.toJson()).toList(),
      'riskScore': riskScore,
      'riskLevel': riskLevel,
      'remediations': remediations,
      'metadata': metadata,
    };
  }
}
