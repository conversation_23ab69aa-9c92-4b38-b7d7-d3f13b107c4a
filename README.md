# 🛡️ Flutter Security Scanner - Agentic AI System

An advanced AI-powered security analysis system for Flutter applications that combines static analysis, dynamic analysis, and vulnerability intelligence to identify security issues and provide actionable remediation guidance.

## 🌟 Features

### 🤖 AI-Powered Analysis

- **LangGraph-based Agent Workflow**: Intelligent orchestration of security analysis tasks
- **Multi-Engine Analysis**: Static AST analysis, dynamic Frida instrumentation, and behavioral analysis
- **RAG-Enhanced Intelligence**: Retrieval-Augmented Generation with real-time vulnerability databases

### 🔍 Comprehensive Security Coverage

- **Certificate Pinning Bypass Detection**
- **TLS/SSL Configuration Analysis**
- **Insecure Data Storage Detection**
- **Method Channel Security Validation**
- **Hardcoded Secrets Detection**
- **Network Security Analysis**
- **WebView Security Assessment**
- **Anti-Tampering Mechanism Checks**

### 🔧 Dynamic Analysis Integration

- **Frida-based Runtime Analysis**: Real-time vulnerability detection during app execution
- **Method Hooking**: Intercept and analyze Flutter method channel communications
- **Memory Analysis**: Detect sensitive data exposure in memory
- **Network Traffic Interception**: Monitor and analyze network communications

### 📊 Intelligence & Reporting

- **Vulnerability Database Integration**: CVE, OSV.dev, OWASP Mobile Top 10
- **Risk Assessment**: Automated risk scoring and prioritization
- **Remediation Planning**: AI-generated fix recommendations with effort estimates
- **Comprehensive Reports**: JSON and human-readable security reports

## 🚀 Quick Start

### Prerequisites

```bash
# Install Dart SDK (3.0.0 or higher)
# Install Flutter SDK (3.0.0 or higher)

# Optional: Install Frida for dynamic analysis
pip install frida-tools
```

### Installation

```bash
# Clone the repository
git clone https://github.com/your-org/flutter_security_scanner.git
cd flutter_security_scanner

# Install dependencies
dart pub get
```

### Basic Usage

```dart
import 'package:flutter_security_scanner/flutter_security_scanner.dart';

void main() async {
  // Initialize the security scanner
  final vulnDb = VulnerabilityDatabase('.cache/vulnerabilities');
  final agent = SecurityAgent(vulnDb);
  await agent.initialize();

  // Analyze a Flutter project
  final report = await agent.analyze('./my_flutter_app');

  print('Risk Level: ${report.riskLevel}');
  print('Findings: ${report.findings.length}');
  print('Vulnerabilities: ${report.vulnerabilities.length}');
}
```

### LangGraph Workflow Usage

```dart
import 'package:flutter_security_scanner/agent/langgraph_workflow.dart';

void main() async {
  final vulnDb = VulnerabilityDatabase('.cache/vulnerabilities');
  final frida = FridaIntegration();
  final workflow = SecurityWorkflowEngine(vulnDb, frida);

  // Execute the complete AI workflow
  final report = await workflow.execute('./my_flutter_app');

  // The workflow automatically:
  // 1. Discovers code files
  // 2. Performs static analysis
  // 3. Queries vulnerability databases
  // 4. Plans and executes dynamic analysis
  // 5. Assesses risk
  // 6. Generates remediation plans
  // 7. Creates comprehensive reports
}
```

## 🏗️ Architecture

### Agent-Based Design

The system follows an agentic AI architecture inspired by LangGraph:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Code Discovery │───▶│ Static Analysis │───▶│Vulnerability DB │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Report Generation│◀───│ Risk Assessment │◀───│Dynamic Analysis │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Components

1. **Security Agent** (`lib/agent/security_agent.dart`)

   - Main orchestrator for security analysis
   - State management and workflow coordination

2. **LangGraph Workflow Engine** (`lib/agent/langgraph_workflow.dart`)

   - Node-based workflow execution
   - Conditional routing and state transitions

3. **Vulnerability Database** (`lib/scanner/vulnerability_database.dart`)

   - RAG-based vulnerability intelligence
   - Integration with CVE, OSV.dev, OWASP databases

4. **Frida Integration** (`lib/scanner/frida_integration.dart`)

   - Dynamic analysis and runtime instrumentation
   - Method hooking and memory analysis

5. **AST Rules Engine** (`lib/scanner/ast_rules.dart`)
   - Static code analysis
   - Pattern-based vulnerability detection

## 🔧 Configuration

### Vulnerability Database Configuration

```dart
final vulnDb = VulnerabilityDatabase('.cache/vulnerabilities');

// Update database from external sources
await vulnDb.updateDatabase();

// Query specific vulnerabilities
final results = await vulnDb.query('flutter certificate pinning',
  minSeverity: Severity.high,
  limit: 10
);
```

### Frida Configuration

```dart
final frida = FridaIntegration();

// Check if Frida is available
final available = await frida.isFridaAvailable();

// Get available devices
final devices = await frida.getAvailableDevices();

// Generate scripts for specific findings
final scripts = frida.generateScriptsForFindings(findings);
```

## 📋 Supported Vulnerability Types

### Static Analysis Rules

| Rule ID | Description                        | Severity |
| ------- | ---------------------------------- | -------- |
| TLS001  | Insecure certificate validation    | Critical |
| STO001  | Insecure data storage              | High     |
| CHAN001 | Method channel security issues     | High     |
| WEB001  | WebView security vulnerabilities   | Medium   |
| CRY001  | Weak cryptographic implementations | Critical |
| NET001  | Insecure network communications    | Medium   |
| SEC001  | Hardcoded secrets detection        | Critical |

### Dynamic Analysis Capabilities

- **Certificate Pinning Bypass**: Detects and attempts to bypass certificate pinning
- **TLS/SSL Bypass**: Identifies TLS/SSL bypass vulnerabilities
- **Method Channel Interception**: Monitors Flutter-native communications
- **Root Detection Bypass**: Tests anti-tampering mechanisms
- **Memory Analysis**: Scans memory for sensitive data exposure
- **Network Traffic Analysis**: Intercepts and analyzes network requests

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
dart test

# Run specific test groups
dart test test/security_agent_test.dart

# Run with coverage
dart test --coverage=coverage
```

## 📊 Example Output

### Security Analysis Report

```json
{
  "projectPath": "./my_flutter_app",
  "analysisDate": "2024-01-15T10:30:00Z",
  "riskScore": 45.5,
  "riskLevel": "HIGH",
  "findings": [
    {
      "file": "lib/network_service.dart",
      "line": 23,
      "ruleId": "TLS001",
      "message": "Insecure certificate validation detected",
      "severity": "critical",
      "confirmed": true
    }
  ],
  "vulnerabilities": [
    {
      "id": "CVE-2023-1234",
      "title": "Flutter Certificate Pinning Bypass",
      "severity": "critical",
      "source": "OSV.dev"
    }
  ],
  "remediations": [
    {
      "finding": "TLS001",
      "priority": "critical",
      "recommendation": "Implement proper certificate validation",
      "effort": "HIGH"
    }
  ]
}
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Clone and setup
git clone https://github.com/your-org/flutter_security_scanner.git
cd flutter_security_scanner
dart pub get

# Run tests
dart test

# Run example
dart run example/main.dart
```

## 📚 Documentation

- [Architecture Overview](docs/agentic_architecture.md)
- [API Reference](docs/api_reference.md)
- [Security Rules Guide](docs/security_rules.md)
- [Frida Integration Guide](docs/frida_integration.md)
- [Contributing Guide](CONTRIBUTING.md)

## 🔒 Security

This tool is designed to find security vulnerabilities. If you discover a security issue in the scanner itself, please report it responsibly to [<EMAIL>](mailto:<EMAIL>).

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Frida](https://frida.re) - Dynamic instrumentation toolkit
- [OSV.dev](https://osv.dev) - Open Source Vulnerability database
- [OWASP Mobile Top 10](https://owasp.org/www-project-mobile-top-10/) - Mobile security guidelines
- [Flutter Security](https://flutter.dev/docs/deployment/security) - Flutter security best practices

---

**⚠️ Disclaimer**: This tool is for educational and authorized security testing purposes only. Always ensure you have proper authorization before testing applications you do not own.
