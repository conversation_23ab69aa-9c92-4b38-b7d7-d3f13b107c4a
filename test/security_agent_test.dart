import 'package:test/test.dart';
import 'dart:io';
import '../lib/agent/security_agent.dart';
import '../lib/agent/langgraph_workflow.dart';
import '../lib/scanner/vulnerability_database.dart';
import '../lib/scanner/frida_integration.dart';
import '../lib/models.dart';

void main() {
  group('Flutter Security Scanner Tests', () {
    late VulnerabilityDatabase vulnDb;
    late FridaIntegration frida;
    late SecurityAgent agent;
    late SecurityWorkflowEngine workflow;

    setUpAll(() async {
      // Create temporary directory for testing
      final tempDir = Directory.systemTemp.createTempSync('flutter_security_test');
      vulnDb = VulnerabilityDatabase(tempDir.path);
      frida = FridaIntegration();
      agent = SecurityAgent(vulnDb);
      workflow = SecurityWorkflowEngine(vulnDb, frida);
      
      await vulnDb.initialize();
      await agent.initialize();
    });

    group('Vulnerability Database Tests', () {
      test('should initialize vulnerability database', () async {
        expect(vulnDb, isNotNull);
      });

      test('should query vulnerabilities by keyword', () async {
        final results = await vulnDb.query('flutter certificate');
        expect(results, isA<List<VulnerabilityEntry>>());
      });

      test('should filter vulnerabilities by severity', () async {
        final results = await vulnDb.query('security', minSeverity: Severity.high);
        for (final result in results) {
          expect([Severity.high, Severity.critical], contains(result.severity));
        }
      });

      test('should update database from external sources', () async {
        await vulnDb.updateDatabase();
        // Database should have some entries after update
        final results = await vulnDb.query('flutter', limit: 1);
        expect(results.length, greaterThanOrEqualTo(0));
      });
    });

    group('Frida Integration Tests', () {
      test('should check Frida availability', () async {
        final available = await frida.isFridaAvailable();
        expect(available, isA<bool>());
      });

      test('should generate scripts for findings', () {
        final findings = [
          Finding(
            file: 'test.dart',
            line: 1,
            ruleId: 'TLS001',
            message: 'Certificate validation issue',
            severity: Severity.critical,
          ),
          Finding(
            file: 'test.dart',
            line: 2,
            ruleId: 'CHAN001',
            message: 'Method channel security issue',
            severity: Severity.high,
          ),
        ];

        final scripts = frida.generateScriptsForFindings(findings);
        expect(scripts.length, greaterThan(0));
        expect(scripts.any((s) => s.name.contains('Certificate')), isTrue);
      });

      test('should get available devices', () async {
        final devices = await frida.getAvailableDevices();
        expect(devices, isA<List<String>>());
      });
    });

    group('Security Agent Tests', () {
      test('should create security agent', () {
        expect(agent, isNotNull);
      });

      test('should analyze project and generate report', () async {
        // Create a temporary test project
        final testProject = await _createTestProject();
        
        try {
          final report = await agent.analyze(testProject.path);
          
          expect(report, isNotNull);
          expect(report.projectPath, equals(testProject.path));
          expect(report.analysisDate, isA<DateTime>());
          expect(report.findings, isA<List<Finding>>());
          expect(report.vulnerabilities, isA<List<VulnerabilityEntry>>());
          expect(report.riskScore, isA<double>());
          expect(report.riskLevel, isA<String>());
          expect(report.remediations, isA<List<Map<String, dynamic>>>());
          
        } finally {
          // Clean up
          await testProject.delete(recursive: true);
        }
      });
    });

    group('LangGraph Workflow Tests', () {
      test('should execute complete workflow', () async {
        final testProject = await _createTestProject();
        
        try {
          final report = await workflow.execute(testProject.path);
          
          expect(report, isNotNull);
          expect(report.projectPath, equals(testProject.path));
          expect(report.metadata['workflowVersion'], equals('1.0.0'));
          expect(report.metadata['executionTime'], isA<int>());
          
        } finally {
          await testProject.delete(recursive: true);
        }
      });
    });

    group('Workflow Nodes Tests', () {
      test('StartNode should initialize context', () async {
        final node = StartNode();
        final context = WorkflowContext(projectPath: '/test');
        
        final result = await node.execute(context);
        
        expect(result.success, isTrue);
        expect(result.updatedContext.state['startTime'], isNotNull);
        expect(result.updatedContext.state['workflowId'], isNotNull);
      });

      test('CodeDiscoveryNode should find Dart files', () async {
        final testProject = await _createTestProject();
        
        try {
          final node = CodeDiscoveryNode();
          final context = WorkflowContext(projectPath: testProject.path);
          
          final result = await node.execute(context);
          
          expect(result.success, isTrue);
          expect(result.updatedContext.state['dartFiles'], isA<List>());
          expect(result.updatedContext.state['totalFiles'], isA<int>());
          
        } finally {
          await testProject.delete(recursive: true);
        }
      });

      test('RiskAssessmentNode should calculate risk score', () async {
        final node = RiskAssessmentNode();
        final findings = [
          Finding(
            file: 'test.dart',
            line: 1,
            ruleId: 'TEST001',
            message: 'Test finding',
            severity: Severity.critical,
          ),
        ];
        
        final context = WorkflowContext(
          projectPath: '/test',
          findings: findings,
        );
        
        final result = await node.execute(context);
        
        expect(result.success, isTrue);
        expect(result.updatedContext.state['riskScore'], isA<double>());
        expect(result.updatedContext.state['riskLevel'], isA<String>());
      });
    });

    group('Security Rules Tests', () {
      test('should detect certificate validation issues', () {
        final code = '''
        client.badCertificateCallback = (cert, host, port) => true;
        ''';
        
        // This would test the AST rules if they were properly implemented
        expect(code.contains('badCertificateCallback'), isTrue);
      });

      test('should detect insecure storage', () {
        final code = '''
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('password', 'secret123');
        ''';
        
        expect(code.contains('SharedPreferences'), isTrue);
        expect(code.contains('password'), isTrue);
      });

      test('should detect hardcoded secrets', () {
        final code = '''
        final apiKey = "sk-1234567890abcdef1234567890abcdef";
        ''';
        
        expect(code.contains('sk-'), isTrue);
      });
    });

    group('Report Generation Tests', () {
      test('should generate JSON report', () {
        final report = SecurityAnalysisReport(
          projectPath: '/test',
          analysisDate: DateTime.now(),
          findings: [
            Finding(
              file: 'test.dart',
              line: 1,
              ruleId: 'TEST001',
              message: 'Test finding',
              severity: Severity.medium,
            ),
          ],
          vulnerabilities: [],
          riskScore: 5.0,
          riskLevel: 'LOW',
          remediations: [],
          metadata: {'test': true},
        );

        final json = report.toJson();
        
        expect(json['projectPath'], equals('/test'));
        expect(json['findings'], isA<List>());
        expect(json['riskScore'], equals(5.0));
        expect(json['riskLevel'], equals('LOW'));
      });
    });

    group('Integration Tests', () {
      test('should perform end-to-end analysis', () async {
        final testProject = await _createVulnerableTestProject();
        
        try {
          // Run both agent and workflow
          final agentReport = await agent.analyze(testProject.path);
          final workflowReport = await workflow.execute(testProject.path);
          
          // Both should find vulnerabilities
          expect(agentReport.findings.length, greaterThan(0));
          expect(workflowReport.findings.length, greaterThan(0));
          
          // Risk scores should be calculated
          expect(agentReport.riskScore, greaterThan(0));
          expect(workflowReport.riskScore, greaterThan(0));
          
          // Should have remediations
          expect(agentReport.remediations.length, greaterThan(0));
          expect(workflowReport.remediations.length, greaterThan(0));
          
        } finally {
          await testProject.delete(recursive: true);
        }
      });
    });
  });
}

/// Create a basic test project
Future<Directory> _createTestProject() async {
  final tempDir = Directory.systemTemp.createTempSync('test_flutter_project');
  
  // Create lib directory
  final libDir = Directory('${tempDir.path}/lib');
  await libDir.create();
  
  // Create a simple main.dart
  final mainFile = File('${tempDir.path}/lib/main.dart');
  await mainFile.writeAsString('''
import 'package:flutter/material.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test App',
      home: Scaffold(
        appBar: AppBar(title: Text('Test')),
        body: Center(child: Text('Hello World')),
      ),
    );
  }
}
''');

  // Create pubspec.yaml
  final pubspecFile = File('${tempDir.path}/pubspec.yaml');
  await pubspecFile.writeAsString('''
name: test_flutter_app
description: Test Flutter app

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true
''');

  return tempDir;
}

/// Create a test project with security vulnerabilities
Future<Directory> _createVulnerableTestProject() async {
  final tempDir = Directory.systemTemp.createTempSync('vulnerable_test_project');
  
  // Create lib directory
  final libDir = Directory('${tempDir.path}/lib');
  await libDir.create();
  
  // Create vulnerable main.dart
  final mainFile = File('${tempDir.path}/lib/main.dart');
  await mainFile.writeAsString('''
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';

void main() {
  runApp(VulnerableApp());
}

class VulnerableApp extends StatelessWidget {
  // VULNERABILITY: Hardcoded API key
  final String apiKey = "sk-1234567890abcdef1234567890abcdef";
  
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Vulnerable App',
      home: VulnerableScreen(),
    );
  }
}

class VulnerableScreen extends StatefulWidget {
  @override
  _VulnerableScreenState createState() => _VulnerableScreenState();
}

class _VulnerableScreenState extends State<VulnerableScreen> {
  @override
  void initState() {
    super.initState();
    _storeCredentials();
  }
  
  void _storeCredentials() async {
    // VULNERABILITY: Insecure storage
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('password', 'mySecretPassword');
    await prefs.setString('token', 'bearer_token_12345');
  }
  
  void _makeInsecureRequest() async {
    // VULNERABILITY: Bad certificate callback
    final client = HttpClient();
    client.badCertificateCallback = (cert, host, port) => true;
    
    try {
      // VULNERABILITY: HTTP instead of HTTPS
      final request = await client.getUrl(Uri.parse('http://api.example.com/data'));
      await request.close();
    } catch (e) {
      print('Error: \$e');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Vulnerable App')),
      body: Center(
        child: ElevatedButton(
          onPressed: _makeInsecureRequest,
          child: Text('Make Request'),
        ),
      ),
    );
  }
}
''');

  // Create pubspec.yaml with dependencies
  final pubspecFile = File('${tempDir.path}/pubspec.yaml');
  await pubspecFile.writeAsString('''
name: vulnerable_test_app
description: Vulnerable test Flutter app

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  shared_preferences: ^2.0.15
  http: ^0.13.5

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true
''');

  return tempDir;
}
